"""
시장 데이터 수집 모듈
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from loguru import logger
import time
import pytz
from src.api.kis_client import KISClient


class MarketDataCollector:
    """시장 데이터 수집기"""
    
    def __init__(self):
        self.kis_client = KISClient()
        self.us_timezone = pytz.timezone('US/Eastern')
        self.kr_timezone = pytz.timezone('Asia/Seoul')
        
    def get_current_price(self, symbol: str, exchange: str = "NAS") -> Optional[Dict]:
        """현재가 조회"""
        try:
            result = self.kis_client.get_overseas_stock_price(symbol, exchange)
            if result and result.get("rt_cd") == "0":
                output = result.get("output", {})
                return {
                    'symbol': symbol,
                    'price': float(output.get("last", 0)),
                    'volume': int(output.get("tvol", 0)),
                    'change': float(output.get("diff", 0)),
                    'change_rate': float(output.get("rate", 0)),
                    'high': float(output.get("high", 0)),
                    'low': float(output.get("low", 0)),
                    'open': float(output.get("open", 0)),
                    'prev_close': float(output.get("base", 0)),
                    'timestamp': datetime.now()
                }
            return None
        except Exception as e:
            logger.error(f"현재가 조회 오류: {e}")
            return None
    
    def get_kline_data(self, symbol: str, exchange: str = "NAS", 
                      period: str = "D", days: int = 100) -> Optional[pd.DataFrame]:
        """K선 데이터 조회"""
        try:
            result = self.kis_client.get_overseas_stock_chart(symbol, exchange, period, days)
            if result and result.get("rt_cd") == "0":
                output2 = result.get("output2", [])
                if not output2:
                    return None
                
                data = []
                for item in output2:
                    data.append({
                        'date': pd.to_datetime(item.get("xymd", "")),
                        'open': float(item.get("open", 0)),
                        'high': float(item.get("high", 0)),
                        'low': float(item.get("low", 0)),
                        'close': float(item.get("clos", 0)),
                        'volume': int(item.get("tvol", 0)),
                        'amount': float(item.get("tamt", 0))
                    })
                
                df = pd.DataFrame(data)
                df = df.sort_values('date').reset_index(drop=True)
                return df
            return None
        except Exception as e:
            logger.error(f"K선 데이터 조회 오류: {e}")
            return None
    
    def get_5min_klines(self, symbol: str, exchange: str = "NAS") -> Optional[List[Dict]]:
        """5분 K선 데이터 조회 (실시간 시뮬레이션)"""
        try:
            # 실제로는 웹소켓이나 별도 API를 사용해야 하지만, 
            # 여기서는 일봉 데이터를 기반으로 5분봉을 시뮬레이션
            daily_data = self.get_kline_data(symbol, exchange, "D", 5)
            if daily_data is None or daily_data.empty:
                return None
            
            # 최근 일봉 데이터를 기반으로 5분봉 생성 (시뮬레이션)
            latest_day = daily_data.iloc[-1]
            current_time = datetime.now(self.us_timezone)
            
            # 장 시작 시간 (9:30 AM ET)
            market_open = current_time.replace(hour=9, minute=30, second=0, microsecond=0)
            
            klines_5m = []
            for i in range(78):  # 6.5시간 * 12개 (5분봉)
                time_5m = market_open + timedelta(minutes=5 * i)
                
                # 가격 변동 시뮬레이션 (실제로는 실시간 데이터 사용)
                price_range = latest_day['high'] - latest_day['low']
                random_factor = np.random.uniform(-0.02, 0.02)
                
                open_price = latest_day['open'] + (price_range * random_factor)
                close_price = open_price + (price_range * np.random.uniform(-0.01, 0.01))
                high_price = max(open_price, close_price) + (price_range * np.random.uniform(0, 0.005))
                low_price = min(open_price, close_price) - (price_range * np.random.uniform(0, 0.005))
                
                volume = int(latest_day['volume'] / 78 * np.random.uniform(0.5, 2.0))
                
                klines_5m.append({
                    'timestamp': time_5m,
                    'open': round(open_price, 2),
                    'high': round(high_price, 2),
                    'low': round(low_price, 2),
                    'close': round(close_price, 2),
                    'volume': volume,
                    'amount': volume * close_price
                })
            
            return klines_5m
            
        except Exception as e:
            logger.error(f"5분 K선 데이터 조회 오류: {e}")
            return None
    
    def get_premarket_high(self, symbol: str, exchange: str = "NAS") -> Optional[float]:
        """장전 최고가 조회 (시뮬레이션)"""
        try:
            # 실제로는 장전 거래 데이터를 조회해야 하지만,
            # 여기서는 전일 종가 기준으로 시뮬레이션
            current_price = self.get_current_price(symbol, exchange)
            if current_price:
                # 장전 최고가를 전일 종가의 ±5% 범위로 시뮬레이션
                prev_close = current_price['prev_close']
                premarket_high = prev_close * np.random.uniform(0.98, 1.05)
                return round(premarket_high, 2)
            return None
        except Exception as e:
            logger.error(f"장전 최고가 조회 오류: {e}")
            return None
    
    def calculate_1min_volume_usd(self, symbol: str, exchange: str = "NAS") -> Optional[float]:
        """1분 거래량(USD) 계산"""
        try:
            current_price = self.get_current_price(symbol, exchange)
            if current_price:
                # 1분 거래량을 전체 거래량에서 추정 (시뮬레이션)
                total_volume = current_price['volume']
                current_time = datetime.now(self.us_timezone)
                market_open = current_time.replace(hour=9, minute=30, second=0, microsecond=0)
                
                if current_time > market_open:
                    minutes_elapsed = (current_time - market_open).total_seconds() / 60
                    avg_volume_per_minute = total_volume / max(minutes_elapsed, 1)
                    
                    # 최근 1분 거래량 시뮬레이션 (평균의 ±50% 범위)
                    volume_1min = avg_volume_per_minute * np.random.uniform(0.5, 1.5)
                    volume_usd = volume_1min * current_price['price']
                    
                    return round(volume_usd, 2)
            return None
        except Exception as e:
            logger.error(f"1분 거래량 계산 오류: {e}")
            return None
    
    def is_market_open(self) -> bool:
        """미국 주식시장 개장 여부 확인"""
        try:
            now = datetime.now(self.us_timezone)
            weekday = now.weekday()
            
            # 주말 제외
            if weekday >= 5:  # 토요일(5), 일요일(6)
                return False
            
            # 장 시간: 9:30 AM - 4:00 PM ET
            market_open = now.replace(hour=9, minute=30, second=0, microsecond=0)
            market_close = now.replace(hour=16, minute=0, second=0, microsecond=0)
            
            return market_open <= now <= market_close
            
        except Exception as e:
            logger.error(f"시장 개장 여부 확인 오류: {e}")
            return False
    
    def get_market_status(self) -> Dict[str, any]:
        """시장 상태 정보"""
        try:
            now_et = datetime.now(self.us_timezone)
            now_kr = datetime.now(self.kr_timezone)
            
            return {
                'is_open': self.is_market_open(),
                'current_time_et': now_et,
                'current_time_kr': now_kr,
                'next_open': self._get_next_market_open(),
                'next_close': self._get_next_market_close()
            }
        except Exception as e:
            logger.error(f"시장 상태 조회 오류: {e}")
            return {}
    
    def _get_next_market_open(self) -> datetime:
        """다음 장 개장 시간"""
        now = datetime.now(self.us_timezone)
        today_open = now.replace(hour=9, minute=30, second=0, microsecond=0)
        
        if now < today_open and now.weekday() < 5:
            return today_open
        else:
            # 다음 영업일 계산
            days_ahead = 1
            if now.weekday() == 4:  # 금요일
                days_ahead = 3
            elif now.weekday() == 5:  # 토요일
                days_ahead = 2
            
            next_open = (now + timedelta(days=days_ahead)).replace(
                hour=9, minute=30, second=0, microsecond=0
            )
            return next_open
    
    def _get_next_market_close(self) -> datetime:
        """다음 장 마감 시간"""
        now = datetime.now(self.us_timezone)
        today_close = now.replace(hour=16, minute=0, second=0, microsecond=0)
        
        if now < today_close and now.weekday() < 5:
            return today_close
        else:
            next_open = self._get_next_market_open()
            return next_open.replace(hour=16, minute=0)
