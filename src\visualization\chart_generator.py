"""
K선 차트 생성기
"""
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from loguru import logger
import config
from src.api.kis_client import KISClient


class ChartGenerator:
    """K선 차트 생성기"""
    
    def __init__(self):
        self.kis_client = KISClient()
    
    def generate_candlestick_chart(self, symbol: str, period: str = "1D", 
                                 days: int = 30) -> Optional[str]:
        """캔들스틱 차트 생성"""
        try:
            # 데이터 조회
            chart_data = self._get_chart_data(symbol, period, days)
            if chart_data is None or chart_data.empty:
                logger.warning(f"{symbol} 차트 데이터 없음")
                return None
            
            # Plotly 캔들스틱 차트 생성
            fig = make_subplots(
                rows=2, cols=1,
                shared_xaxes=True,
                vertical_spacing=0.1,
                subplot_titles=(f'{symbol} 주가', '거래량'),
                row_width=[0.7, 0.3]
            )
            
            # 캔들스틱 차트
            fig.add_trace(
                go.Candlestick(
                    x=chart_data['date'],
                    open=chart_data['open'],
                    high=chart_data['high'],
                    low=chart_data['low'],
                    close=chart_data['close'],
                    name=symbol,
                    increasing_line_color='#00ff00',
                    decreasing_line_color='#ff0000'
                ),
                row=1, col=1
            )
            
            # 거래량 차트
            colors = ['red' if close < open else 'green' 
                     for close, open in zip(chart_data['close'], chart_data['open'])]
            
            fig.add_trace(
                go.Bar(
                    x=chart_data['date'],
                    y=chart_data['volume'],
                    name='거래량',
                    marker_color=colors,
                    opacity=0.7
                ),
                row=2, col=1
            )
            
            # 이동평균선 추가
            if len(chart_data) >= 20:
                ma20 = chart_data['close'].rolling(window=20).mean()
                fig.add_trace(
                    go.Scatter(
                        x=chart_data['date'],
                        y=ma20,
                        mode='lines',
                        name='MA20',
                        line=dict(color='blue', width=1)
                    ),
                    row=1, col=1
                )
            
            if len(chart_data) >= 5:
                ma5 = chart_data['close'].rolling(window=5).mean()
                fig.add_trace(
                    go.Scatter(
                        x=chart_data['date'],
                        y=ma5,
                        mode='lines',
                        name='MA5',
                        line=dict(color='orange', width=1)
                    ),
                    row=1, col=1
                )
            
            # 레이아웃 설정
            fig.update_layout(
                title=f'{symbol} - {period} 차트',
                xaxis_rangeslider_visible=False,
                height=600,
                showlegend=True,
                template='plotly_white'
            )
            
            fig.update_xaxes(title_text="날짜", row=2, col=1)
            fig.update_yaxes(title_text="가격 ($)", row=1, col=1)
            fig.update_yaxes(title_text="거래량", row=2, col=1)
            
            # HTML로 변환
            chart_html = fig.to_html(include_plotlyjs='cdn', div_id=f"chart_{symbol}")
            return chart_html
            
        except Exception as e:
            logger.error(f"{symbol} 차트 생성 오류: {e}")
            return None
    
    def generate_intraday_chart(self, symbol: str) -> Optional[str]:
        """일중 차트 생성 (5분봉)"""
        try:
            # 5분봉 데이터 생성 (모의)
            intraday_data = self._generate_intraday_data(symbol)
            if intraday_data is None or intraday_data.empty:
                return None
            
            fig = make_subplots(
                rows=2, cols=1,
                shared_xaxes=True,
                vertical_spacing=0.1,
                subplot_titles=(f'{symbol} 일중 차트 (5분봉)', '거래량'),
                row_width=[0.7, 0.3]
            )
            
            # 5분봉 캔들스틱
            fig.add_trace(
                go.Candlestick(
                    x=intraday_data['timestamp'],
                    open=intraday_data['open'],
                    high=intraday_data['high'],
                    low=intraday_data['low'],
                    close=intraday_data['close'],
                    name=f'{symbol} 5분봉',
                    increasing_line_color='#26a69a',
                    decreasing_line_color='#ef5350'
                ),
                row=1, col=1
            )
            
            # 거래량
            colors = ['#ef5350' if close < open else '#26a69a' 
                     for close, open in zip(intraday_data['close'], intraday_data['open'])]
            
            fig.add_trace(
                go.Bar(
                    x=intraday_data['timestamp'],
                    y=intraday_data['volume'],
                    name='거래량',
                    marker_color=colors,
                    opacity=0.6
                ),
                row=2, col=1
            )
            
            # 레이아웃 설정
            fig.update_layout(
                title=f'{symbol} 일중 차트 - {datetime.now().strftime("%Y-%m-%d")}',
                xaxis_rangeslider_visible=False,
                height=500,
                showlegend=True,
                template='plotly_white'
            )
            
            fig.update_xaxes(title_text="시간", row=2, col=1)
            fig.update_yaxes(title_text="가격 ($)", row=1, col=1)
            fig.update_yaxes(title_text="거래량", row=2, col=1)
            
            return fig.to_html(include_plotlyjs='cdn', div_id=f"intraday_{symbol}")
            
        except Exception as e:
            logger.error(f"{symbol} 일중 차트 생성 오류: {e}")
            return None
    
    def generate_mini_chart(self, symbol: str, width: int = 300, height: int = 200) -> Optional[str]:
        """미니 차트 생성 (목록용)"""
        try:
            # 간단한 라인 차트 데이터
            chart_data = self._get_chart_data(symbol, "1D", 7)  # 7일 데이터
            if chart_data is None or chart_data.empty:
                return None
            
            fig = go.Figure()
            
            # 종가 라인
            fig.add_trace(
                go.Scatter(
                    x=chart_data['date'],
                    y=chart_data['close'],
                    mode='lines',
                    name=symbol,
                    line=dict(color='#2196F3', width=2),
                    fill='tonexty' if len(chart_data) > 1 else None,
                    fillcolor='rgba(33, 150, 243, 0.1)'
                )
            )
            
            # 레이아웃 설정 (미니 차트용)
            fig.update_layout(
                width=width,
                height=height,
                margin=dict(l=10, r=10, t=10, b=10),
                showlegend=False,
                xaxis=dict(showgrid=False, showticklabels=False),
                yaxis=dict(showgrid=False, showticklabels=False),
                plot_bgcolor='rgba(0,0,0,0)',
                paper_bgcolor='rgba(0,0,0,0)'
            )
            
            return fig.to_html(include_plotlyjs='cdn', div_id=f"mini_{symbol}")
            
        except Exception as e:
            logger.error(f"{symbol} 미니 차트 생성 오류: {e}")
            return None
    
    def _get_chart_data(self, symbol: str, period: str, days: int) -> Optional[pd.DataFrame]:
        """차트 데이터 조회"""
        try:
            if config.DEMO_MODE:
                return self._generate_mock_chart_data(symbol, days)
            else:
                # 실제 API 호출
                result = self.kis_client.get_overseas_stock_chart(symbol, "NAS", period, days)
                if result and result.get("rt_cd") == "0":
                    output2 = result.get("output2", [])
                    if not output2:
                        return None
                    
                    data = []
                    for item in output2:
                        data.append({
                            'date': pd.to_datetime(item.get("xymd", "")),
                            'open': float(item.get("open", 0)),
                            'high': float(item.get("high", 0)),
                            'low': float(item.get("low", 0)),
                            'close': float(item.get("clos", 0)),
                            'volume': int(item.get("tvol", 0))
                        })
                    
                    df = pd.DataFrame(data)
                    return df.sort_values('date').reset_index(drop=True)
                
            return None
            
        except Exception as e:
            logger.error(f"{symbol} 차트 데이터 조회 오류: {e}")
            return None
    
    def _generate_mock_chart_data(self, symbol: str, days: int) -> pd.DataFrame:
        """모의 차트 데이터 생성"""
        try:
            # 기본 가격 설정
            base_prices = {
                "AAPL": 175.0, "MSFT": 380.0, "GOOGL": 140.0, "AMZN": 155.0,
                "TSLA": 245.0, "META": 325.0, "NVDA": 485.0, "NFLX": 445.0,
                "SPCE": 8.5, "CLOV": 3.2, "WISH": 1.8, "SOFI": 7.5
            }
            
            base_price = base_prices.get(symbol, 100.0)
            
            # 날짜 범위 생성
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            date_range = pd.bdate_range(start=start_date, end=end_date)
            
            data = []
            current_price = base_price
            
            for date in date_range:
                # 일일 변동률 (-5% ~ +5%)
                daily_change = np.random.uniform(-0.05, 0.05)
                
                # OHLC 생성
                open_price = current_price
                close_price = open_price * (1 + daily_change)
                
                high_price = max(open_price, close_price) * np.random.uniform(1.0, 1.03)
                low_price = min(open_price, close_price) * np.random.uniform(0.97, 1.0)
                
                volume = np.random.randint(100000, 5000000)
                
                data.append({
                    'date': date,
                    'open': round(open_price, 2),
                    'high': round(high_price, 2),
                    'low': round(low_price, 2),
                    'close': round(close_price, 2),
                    'volume': volume
                })
                
                current_price = close_price
            
            return pd.DataFrame(data)
            
        except Exception as e:
            logger.error(f"모의 차트 데이터 생성 오류: {e}")
            return pd.DataFrame()
    
    def _generate_intraday_data(self, symbol: str) -> pd.DataFrame:
        """일중 5분봉 데이터 생성"""
        try:
            # 오늘 장 시간 (9:30 AM - 4:00 PM ET)
            today = datetime.now().replace(hour=9, minute=30, second=0, microsecond=0)
            
            # 5분 간격으로 78개 데이터 포인트 (6.5시간)
            timestamps = []
            for i in range(78):
                timestamps.append(today + timedelta(minutes=5 * i))
            
            # 기본 가격
            base_prices = {
                "AAPL": 175.0, "MSFT": 380.0, "GOOGL": 140.0, "AMZN": 155.0,
                "TSLA": 245.0, "META": 325.0, "NVDA": 485.0, "NFLX": 445.0,
                "SPCE": 8.5, "CLOV": 3.2, "WISH": 1.8, "SOFI": 7.5
            }
            
            base_price = base_prices.get(symbol, 100.0)
            current_price = base_price
            
            data = []
            for timestamp in timestamps:
                # 5분 변동률
                change = np.random.uniform(-0.02, 0.02)
                
                open_price = current_price
                close_price = open_price * (1 + change)
                
                high_price = max(open_price, close_price) * np.random.uniform(1.0, 1.01)
                low_price = min(open_price, close_price) * np.random.uniform(0.99, 1.0)
                
                volume = np.random.randint(10000, 500000)
                
                data.append({
                    'timestamp': timestamp,
                    'open': round(open_price, 2),
                    'high': round(high_price, 2),
                    'low': round(low_price, 2),
                    'close': round(close_price, 2),
                    'volume': volume
                })
                
                current_price = close_price
            
            return pd.DataFrame(data)
            
        except Exception as e:
            logger.error(f"일중 데이터 생성 오류: {e}")
            return pd.DataFrame()
