"""
백테스터 테스트
"""
import unittest
import pandas as pd
from datetime import datetime
from src.testing.backtester import Backtester, MockMarketData


class TestBacktester(unittest.TestCase):
    """백테스터 테스트"""
    
    def setUp(self):
        """테스트 설정"""
        self.backtester = Backtester(initial_capital=10000)
    
    def test_mock_data_generation(self):
        """모의 데이터 생성 테스트"""
        mock_data = MockMarketData("2024-01-01", "2024-01-31")
        
        # 일봉 데이터 생성
        daily_data = mock_data.generate_stock_data("AAPL", 150.0)
        
        # 검증
        self.assertFalse(daily_data.empty)
        self.assertIn('open', daily_data.columns)
        self.assertIn('high', daily_data.columns)
        self.assertIn('low', daily_data.columns)
        self.assertIn('close', daily_data.columns)
        self.assertIn('volume', daily_data.columns)
        
        # 5분봉 데이터 생성
        min5_data = mock_data.generate_5min_data(daily_data.head(1))
        
        # 검증
        self.assertFalse(min5_data.empty)
        self.assertEqual(len(min5_data), 78)  # 하루 78개 5분봉
    
    def test_backtester_initialization(self):
        """백테스터 초기화 테스트"""
        self.assertEqual(self.backtester.initial_capital, 10000)
        self.assertEqual(self.backtester.current_capital, 10000)
        self.assertEqual(len(self.backtester.positions), 0)
        self.assertEqual(len(self.backtester.trade_log), 0)
    
    def test_execute_buy(self):
        """매수 실행 테스트"""
        from datetime import date
        
        # 매수 실행
        self.backtester._execute_buy("AAPL", 150.0, date.today())
        
        # 검증
        self.assertIn("AAPL", self.backtester.positions)
        position = self.backtester.positions["AAPL"]
        self.assertEqual(position['avg_price'], 150.0)
        self.assertGreater(position['quantity'], 0)
        
        # 거래 로그 확인
        self.assertEqual(len(self.backtester.trade_log), 1)
        trade = self.backtester.trade_log[0]
        self.assertEqual(trade['action'], 'BUY')
        self.assertEqual(trade['symbol'], 'AAPL')
    
    def test_execute_sell(self):
        """매도 실행 테스트"""
        from datetime import date
        
        # 먼저 매수
        self.backtester._execute_buy("AAPL", 150.0, date.today())
        initial_capital = self.backtester.current_capital
        
        # 매도 실행
        self.backtester._execute_sell("AAPL", 160.0, date.today(), "익절")
        
        # 검증
        self.assertNotIn("AAPL", self.backtester.positions)
        self.assertGreater(self.backtester.current_capital, initial_capital)
        
        # 거래 로그 확인
        self.assertEqual(len(self.backtester.trade_log), 2)
        sell_trade = self.backtester.trade_log[1]
        self.assertEqual(sell_trade['action'], 'SELL')
        self.assertGreater(sell_trade['pnl'], 0)  # 수익
    
    def test_simulate_strategy(self):
        """전략 시뮬레이션 테스트"""
        # 짧은 기간으로 테스트
        result = self.backtester.simulate_strategy(
            symbols=["AAPL"],
            start_date="2024-01-01",
            end_date="2024-01-05"
        )
        
        # 검증
        self.assertIsNotNone(result)
        self.assertIsInstance(result.total_return, float)
        self.assertIsInstance(result.total_trades, int)
        self.assertFalse(result.equity_curve.empty)


if __name__ == '__main__':
    unittest.main()
