"""
거래 전략 테스트
"""
import unittest
from unittest.mock import Mock, patch
import pandas as pd
from datetime import datetime
from src.strategy.trading_strategy import TradingStrategy, SignalType


class TestTradingStrategy(unittest.TestCase):
    """거래 전략 테스트"""
    
    def setUp(self):
        """테스트 설정"""
        self.strategy = TradingStrategy()
    
    @patch('src.strategy.trading_strategy.MarketDataCollector')
    def test_analyze_entry_conditions_buy_signal(self, mock_market_data):
        """매수 신호 테스트"""
        # Mock 데이터 설정
        mock_market_data_instance = Mock()
        mock_market_data.return_value = mock_market_data_instance
        
        # 현재가 데이터 모킹
        mock_market_data_instance.get_current_price.return_value = {
            'price': 150.0,
            'open': 145.0,
            'high': 148.0
        }
        
        # 5분봉 데이터 모킹
        mock_market_data_instance.get_5min_klines.return_value = [
            {'close': 146.0},  # 첫 번째 5분봉
            {'close': 147.0}   # 두 번째 5분봉 (일시가 145.0보다 높음)
        ]
        
        # 장전 최고가 모킹
        mock_market_data_instance.get_premarket_high.return_value = 149.0
        
        # 1분 거래량 모킹
        mock_market_data_instance.calculate_1min_volume_usd.return_value = 350000
        
        self.strategy.market_data = mock_market_data_instance
        
        # 테스트 실행
        signal = self.strategy.analyze_entry_conditions("AAPL")
        
        # 검증
        self.assertIsNotNone(signal)
        self.assertEqual(signal.signal_type, SignalType.BUY)
        self.assertEqual(signal.symbol, "AAPL")
        self.assertEqual(signal.price, 150.0)
    
    @patch('src.strategy.trading_strategy.MarketDataCollector')
    def test_analyze_entry_conditions_hold_signal(self, mock_market_data):
        """보유 신호 테스트"""
        # Mock 데이터 설정
        mock_market_data_instance = Mock()
        mock_market_data.return_value = mock_market_data_instance
        
        # 조건을 만족하지 않는 데이터
        mock_market_data_instance.get_current_price.return_value = {
            'price': 145.0,
            'open': 145.0,
            'high': 148.0
        }
        
        mock_market_data_instance.get_5min_klines.return_value = [
            {'close': 146.0},
            {'close': 144.0}  # 일시가보다 낮음
        ]
        
        mock_market_data_instance.get_premarket_high.return_value = 149.0
        mock_market_data_instance.calculate_1min_volume_usd.return_value = 200000  # 임계값 미달
        
        self.strategy.market_data = mock_market_data_instance
        
        # 테스트 실행
        signal = self.strategy.analyze_entry_conditions("AAPL")
        
        # 검증
        self.assertIsNotNone(signal)
        self.assertEqual(signal.signal_type, SignalType.HOLD)
    
    def test_update_position_new_buy(self):
        """새 매수 포지션 테스트"""
        # 테스트 실행
        self.strategy.update_position("AAPL", 100, 150.0, "BUY")
        
        # 검증
        self.assertIn("AAPL", self.strategy.positions)
        position = self.strategy.positions["AAPL"]
        self.assertEqual(position.quantity, 100)
        self.assertEqual(position.avg_price, 150.0)
    
    def test_update_position_sell(self):
        """매도 포지션 테스트"""
        # 초기 포지션 설정
        self.strategy.update_position("AAPL", 100, 150.0, "BUY")
        
        # 부분 매도
        self.strategy.update_position("AAPL", 50, 160.0, "SELL")
        
        # 검증
        self.assertIn("AAPL", self.strategy.positions)
        position = self.strategy.positions["AAPL"]
        self.assertEqual(position.quantity, 50)
        
        # 전량 매도
        self.strategy.update_position("AAPL", 50, 160.0, "SELL")
        
        # 검증
        self.assertNotIn("AAPL", self.strategy.positions)


if __name__ == '__main__':
    unittest.main()
