"""
한국투자증권 Open API 해외주식 자동매매 프로그램
메인 실행 파일
"""
import time
import schedule
from datetime import datetime
from loguru import logger
import config
from src.trading.order_manager import OrderManager
from src.data.market_data import MarketDataCollector
from src.testing.backtester import Backtester


class TradingBot:
    """자동매매 봇"""
    
    def __init__(self):
        self.order_manager = OrderManager()
        self.market_data = MarketDataCollector()
        self.is_running = False
        self.target_symbols = ["AAPL", "TSLA", "NVDA", "MSFT", "GOOGL"]  # 거래 대상 종목
        
        # 로깅 설정
        self._setup_logging()
        
    def _setup_logging(self):
        """로깅 설정"""
        logger.remove()  # 기본 핸들러 제거
        
        # 콘솔 로깅
        logger.add(
            sink=lambda msg: print(msg, end=""),
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
            level=config.LOG_CONFIG['level']
        )
        
        # 파일 로깅
        logger.add(
            sink=config.LOG_CONFIG['file_path'],
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            level=config.LOG_CONFIG['level'],
            rotation=config.LOG_CONFIG['max_file_size'],
            retention=config.LOG_CONFIG['backup_count']
        )
        
        logger.info("로깅 시스템 초기화 완료")
    
    def start(self):
        """봇 시작"""
        try:
            logger.info("=== 한국투자증권 자동매매 봇 시작 ===")
            
            # API 연결 테스트
            if not self._test_api_connection():
                logger.error("API 연결 실패. 프로그램을 종료합니다.")
                return
            
            # 스케줄 설정
            self._setup_schedule()
            
            self.is_running = True
            logger.info("자동매매 봇이 시작되었습니다.")
            
            # 메인 루프
            while self.is_running:
                try:
                    schedule.run_pending()
                    time.sleep(1)
                except KeyboardInterrupt:
                    logger.info("사용자에 의해 중단되었습니다.")
                    self.stop()
                except Exception as e:
                    logger.error(f"메인 루프 오류: {e}")
                    time.sleep(5)
                    
        except Exception as e:
            logger.error(f"봇 시작 오류: {e}")
    
    def stop(self):
        """봇 중지"""
        self.is_running = False
        logger.info("자동매매 봇이 중지되었습니다.")
    
    def _test_api_connection(self) -> bool:
        """API 연결 테스트"""
        try:
            logger.info("API 연결 테스트 중...")
            
            # 토큰 발급 테스트
            if not self.order_manager.kis_client.get_access_token():
                logger.error("토큰 발급 실패")
                return False
            
            # 시장 데이터 테스트
            test_symbol = "AAPL"
            price_data = self.market_data.get_current_price(test_symbol)
            if not price_data:
                logger.error("시장 데이터 조회 실패")
                return False
            
            logger.info(f"API 연결 성공 - {test_symbol}: ${price_data['price']:.2f}")
            return True
            
        except Exception as e:
            logger.error(f"API 연결 테스트 오류: {e}")
            return False
    
    def _setup_schedule(self):
        """스케줄 설정"""
        try:
            # 시장 개장 시간에 맞춰 스케줄 설정
            
            # 매 분마다 거래 신호 확인 (시장 개장 시간에만)
            schedule.every().minute.do(self._check_trading_signals)
            
            # 매 5분마다 포지션 상태 확인
            schedule.every(5).minutes.do(self._check_positions)
            
            # 매 시간마다 시스템 상태 확인
            schedule.every().hour.do(self._system_health_check)
            
            # 매일 오전 9시에 일일 리포트
            schedule.every().day.at("09:00").do(self._daily_report)
            
            # 매일 오후 4시 30분에 일일 정산
            schedule.every().day.at("16:30").do(self._daily_settlement)
            
            logger.info("스케줄 설정 완료")
            
        except Exception as e:
            logger.error(f"스케줄 설정 오류: {e}")
    
    def _check_trading_signals(self):
        """거래 신호 확인"""
        try:
            # 시장 개장 시간 확인
            if not self.market_data.is_market_open():
                return
            
            logger.debug("거래 신호 확인 중...")
            
            # 각 종목에 대해 거래 신호 처리
            executed_orders = self.order_manager.process_trading_signals(self.target_symbols)
            
            if executed_orders:
                logger.info(f"{len(executed_orders)}개 주문 실행됨")
                for order in executed_orders:
                    logger.info(f"주문: {order.symbol} {order.side} {order.quantity}주 @ ${order.price:.2f}")
            
        except Exception as e:
            logger.error(f"거래 신호 확인 오류: {e}")
    
    def _check_positions(self):
        """포지션 상태 확인"""
        try:
            positions = self.order_manager.strategy.get_current_positions()
            
            if positions:
                logger.info(f"현재 {len(positions)}개 포지션 보유 중")
                for symbol, position in positions.items():
                    pnl_percent = position.unrealized_pnl_percent * 100
                    logger.info(f"{symbol}: {position.quantity}주, PnL: {pnl_percent:+.2f}%")
            
        except Exception as e:
            logger.error(f"포지션 확인 오류: {e}")
    
    def _system_health_check(self):
        """시스템 상태 확인"""
        try:
            logger.info("시스템 상태 확인 중...")
            
            # API 토큰 유효성 확인
            if not self.order_manager.kis_client.is_token_valid():
                logger.warning("토큰 갱신 필요")
                self.order_manager.kis_client.get_access_token()
            
            # 포트폴리오 요약
            portfolio = self.order_manager.get_portfolio_summary()
            if portfolio:
                logger.info(f"포트폴리오 현황: 총 가치 ${portfolio['total_value']:.2f}, "
                           f"PnL: {portfolio['total_pnl_percent']:.2%}")
            
        except Exception as e:
            logger.error(f"시스템 상태 확인 오류: {e}")
    
    def _daily_report(self):
        """일일 리포트"""
        try:
            logger.info("=== 일일 리포트 ===")
            
            # 시장 상태
            market_status = self.market_data.get_market_status()
            logger.info(f"시장 상태: {'개장' if market_status.get('is_open') else '휴장'}")
            
            # 포트폴리오 요약
            portfolio = self.order_manager.get_portfolio_summary()
            if portfolio:
                logger.info(f"포지션 수: {portfolio['total_positions']}")
                logger.info(f"총 가치: ${portfolio['total_value']:.2f}")
                logger.info(f"일일 PnL: ${portfolio['daily_pnl']:.2f}")
                logger.info(f"일일 거래 횟수: {portfolio['daily_trade_count']}")
            
            # 리스크 관리 상태 리셋
            self.order_manager.risk_manager.reset_daily_stats()
            
        except Exception as e:
            logger.error(f"일일 리포트 오류: {e}")
    
    def _daily_settlement(self):
        """일일 정산"""
        try:
            logger.info("=== 일일 정산 ===")
            
            # 모든 포지션 현황 출력
            positions = self.order_manager.strategy.get_current_positions()
            for symbol, position in positions.items():
                logger.info(f"{symbol}: {position.quantity}주, "
                           f"평균단가: ${position.avg_price:.2f}, "
                           f"현재가: ${position.current_price:.2f}, "
                           f"PnL: ${position.unrealized_pnl:.2f} ({position.unrealized_pnl_percent:.2%})")
            
            # 체결된 주문 요약
            filled_orders = self.order_manager.get_filled_orders()
            if filled_orders:
                logger.info(f"오늘 체결된 주문: {len(filled_orders)}건")
                
        except Exception as e:
            logger.error(f"일일 정산 오류: {e}")


def run_backtest():
    """백테스트 실행"""
    try:
        logger.info("=== 백테스트 시작 ===")
        
        backtester = Backtester(initial_capital=10000)
        symbols = ["AAPL", "TSLA", "NVDA"]
        
        result = backtester.simulate_strategy(
            symbols=symbols,
            start_date=config.BACKTEST_CONFIG['start_date'],
            end_date=config.BACKTEST_CONFIG['end_date']
        )
        
        if result:
            logger.info("=== 백테스트 결과 ===")
            logger.info(f"총 수익률: {result.total_return:.2%}")
            logger.info(f"연간 수익률: {result.annual_return:.2%}")
            logger.info(f"샤프 비율: {result.sharpe_ratio:.2f}")
            logger.info(f"최대 낙폭: {result.max_drawdown:.2%}")
            logger.info(f"승률: {result.win_rate:.2%}")
            logger.info(f"총 거래 횟수: {result.total_trades}")
            logger.info(f"평균 수익: ${result.avg_win:.2f}")
            logger.info(f"평균 손실: ${result.avg_loss:.2f}")
            
            # 결과 저장
            if not result.equity_curve.empty:
                result.equity_curve.to_csv('backtest_equity_curve.csv', index=False)
                logger.info("자본 곡선이 backtest_equity_curve.csv에 저장되었습니다.")
            
            if not result.trade_log.empty:
                result.trade_log.to_csv('backtest_trade_log.csv', index=False)
                logger.info("거래 로그가 backtest_trade_log.csv에 저장되었습니다.")
        
    except Exception as e:
        logger.error(f"백테스트 오류: {e}")


def main():
    """메인 함수"""
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "backtest":
        # 백테스트 모드
        run_backtest()
    else:
        # 실제 거래 모드
        bot = TradingBot()
        try:
            bot.start()
        except KeyboardInterrupt:
            logger.info("프로그램이 사용자에 의해 중단되었습니다.")
        except Exception as e:
            logger.error(f"프로그램 실행 오류: {e}")
        finally:
            bot.stop()


if __name__ == "__main__":
    main()
