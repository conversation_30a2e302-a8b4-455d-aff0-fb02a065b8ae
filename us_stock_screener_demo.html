<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>미국 주식 스크리너 - 20% 이상 상승 종목</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        .stock-card {
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
        }
        .stock-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .gain-positive { color: #28a745; font-weight: bold; }
        .gain-negative { color: #dc3545; font-weight: bold; }
        .criteria-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
        }
        .top-gainers {
            max-height: 400px;
            overflow-y: auto;
        }
        .demo-badge {
            position: fixed;
            top: 10px;
            left: 10px;
            z-index: 1001;
        }
    </style>
</head>
<body class="bg-light">
    <!-- 데모 배지 -->
    <div class="demo-badge">
        <span class="badge bg-warning text-dark fs-6">
            <i class="fas fa-flask"></i> 데모 모드
        </span>
    </div>

    <div class="container-fluid py-4">
        <!-- 헤더 -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="h3 mb-3">
                    <i class="fas fa-search text-primary"></i>
                    미국 주식 스크리너 (데모)
                </h1>
                <p class="text-muted">개장 후 20% 이상 상승한 종목을 찾아보세요</p>
            </div>
        </div>

        <!-- 스크리닝 조건 설정 -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-filter"></i> 스크리닝 조건
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="screeningForm" class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">최소 상승률 (%)</label>
                                <input type="number" class="form-control" id="minGain" value="20" min="0" max="100" step="0.1">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">최소 거래량</label>
                                <input type="number" class="form-control" id="minVolume" value="100000" min="0" step="10000">
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> 스크리닝 실행
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="refreshData()">
                                    <i class="fas fa-sync-alt"></i> 새로고침
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-trophy"></i> 상위 상승 종목
                        </h6>
                    </div>
                    <div class="card-body top-gainers" id="topGainers">
                        <!-- 상위 상승 종목이 여기에 표시됩니다 -->
                    </div>
                </div>
            </div>
        </div>

        <!-- 스크리닝 결과 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list"></i> 스크리닝 결과
                            <span id="resultCount" class="badge bg-primary ms-2">0</span>
                        </h5>
                        <div id="criteriaDisplay"></div>
                    </div>
                    <div class="card-body">
                        <div id="stockResults" class="row">
                            <div class="col-12 text-center text-muted py-5">
                                <i class="fas fa-search fa-3x mb-3"></i>
                                <p>스크리닝 조건을 설정하고 "스크리닝 실행" 버튼을 클릭하세요</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 종목 상세 모달 -->
    <div class="modal fade" id="stockModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">종목 상세 정보</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div id="stockInfo">
                                <!-- 종목 정보가 여기에 표시됩니다 -->
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div id="stockChart" style="height: 400px;">
                                <!-- 차트가 여기에 표시됩니다 -->
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary active" onclick="loadChart('daily')">일봉</button>
                                <button type="button" class="btn btn-outline-primary" onclick="loadChart('intraday')">일중</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 모의 주식 데이터
        const mockStocks = [
            { symbol: 'SPCE', company_name: 'Virgin Galactic Holdings', current_price: 12.45, open_price: 8.50, change_percent: 46.47, volume: 2500000, sector: 'Aerospace' },
            { symbol: 'CLOV', company_name: 'Clover Health Investments', current_price: 4.20, open_price: 3.20, change_percent: 31.25, volume: 1800000, sector: 'Healthcare' },
            { symbol: 'WISH', company_name: 'ContextLogic Inc.', current_price: 2.65, open_price: 1.80, change_percent: 47.22, volume: 3200000, sector: 'E-commerce' },
            { symbol: 'SOFI', company_name: 'SoFi Technologies Inc.', current_price: 9.85, open_price: 7.50, change_percent: 31.33, volume: 1500000, sector: 'Fintech' },
            { symbol: 'GME', company_name: 'GameStop Corp.', current_price: 24.80, open_price: 18.50, change_percent: 34.05, volume: 4500000, sector: 'Gaming' },
            { symbol: 'AMC', company_name: 'AMC Entertainment Holdings', current_price: 7.25, open_price: 5.20, change_percent: 39.42, volume: 6800000, sector: 'Entertainment' },
            { symbol: 'MRNA', company_name: 'Moderna Inc.', current_price: 125.60, open_price: 95.00, change_percent: 32.21, volume: 980000, sector: 'Biotechnology' },
            { symbol: 'COIN', company_name: 'Coinbase Global Inc.', current_price: 112.30, open_price: 85.00, change_percent: 32.12, volume: 1200000, sector: 'Cryptocurrency' },
            { symbol: 'RIVN', company_name: 'Rivian Automotive Inc.', current_price: 18.75, open_price: 14.20, change_percent: 32.04, volume: 2100000, sector: 'Electric Vehicles' },
            { symbol: 'LCID', company_name: 'Lucid Group Inc.', current_price: 6.85, open_price: 5.10, change_percent: 34.31, volume: 1900000, sector: 'Electric Vehicles' }
        ];

        const topGainersData = [
            { symbol: 'WISH', company_name: 'ContextLogic Inc.', current_price: 2.65, change_percent: 47.22 },
            { symbol: 'SPCE', company_name: 'Virgin Galactic Holdings', current_price: 12.45, change_percent: 46.47 },
            { symbol: 'AMC', company_name: 'AMC Entertainment Holdings', current_price: 7.25, change_percent: 39.42 },
            { symbol: 'LCID', company_name: 'Lucid Group Inc.', current_price: 6.85, change_percent: 34.31 },
            { symbol: 'GME', company_name: 'GameStop Corp.', current_price: 24.80, change_percent: 34.05 },
            { symbol: 'RIVN', company_name: 'Rivian Automotive Inc.', current_price: 18.75, change_percent: 32.04 },
            { symbol: 'COIN', company_name: 'Coinbase Global Inc.', current_price: 112.30, change_percent: 32.12 },
            { symbol: 'MRNA', company_name: 'Moderna Inc.', current_price: 125.60, change_percent: 32.21 },
            { symbol: 'SOFI', company_name: 'SoFi Technologies Inc.', current_price: 9.85, change_percent: 31.33 },
            { symbol: 'CLOV', company_name: 'Clover Health Investments', current_price: 4.20, change_percent: 31.25 }
        ];

        let currentSymbol = '';

        // 페이지 로드 시 초기화
        document.addEventListener('DOMContentLoaded', function() {
            loadTopGainers();
            showNotification('미국 주식 스크리너 데모에 오신 것을 환영합니다!', 'info');
        });

        // 스크리닝 폼 제출
        document.getElementById('screeningForm').addEventListener('submit', function(e) {
            e.preventDefault();
            runScreening();
        });

        // 스크리닝 실행
        function runScreening() {
            const minGain = parseFloat(document.getElementById('minGain').value);
            const minVolume = parseInt(document.getElementById('minVolume').value);
            
            // 조건에 맞는 종목 필터링
            const filteredStocks = mockStocks.filter(stock => 
                stock.change_percent >= minGain && stock.volume >= minVolume
            );
            
            displayResults(filteredStocks, { min_gain_percent: minGain, min_volume: minVolume });
            document.getElementById('resultCount').textContent = filteredStocks.length;
        }

        // 결과 표시
        function displayResults(stocks, criteria) {
            const container = document.getElementById('stockResults');
            
            if (stocks.length === 0) {
                container.innerHTML = `
                    <div class="col-12 text-center text-muted py-5">
                        <i class="fas fa-search fa-3x mb-3"></i>
                        <p>조건에 맞는 종목이 없습니다</p>
                        <small>다른 조건으로 다시 검색해보세요</small>
                    </div>
                `;
                return;
            }
            
            // 조건 표시
            document.getElementById('criteriaDisplay').innerHTML = `
                <span class="criteria-badge">
                    상승률 ≥ ${criteria.min_gain_percent}% | 거래량 ≥ ${criteria.min_volume.toLocaleString()}
                </span>
            `;
            
            container.innerHTML = '';
            
            stocks.forEach(stock => {
                const card = document.createElement('div');
                card.className = 'col-md-6 col-lg-4 mb-3';
                card.innerHTML = `
                    <div class="card stock-card h-100" onclick="showStockDetail('${stock.symbol}')">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <div>
                                    <h6 class="card-title mb-1">${stock.symbol}</h6>
                                    <small class="text-muted">${stock.company_name}</small>
                                </div>
                                <span class="badge bg-success">+${stock.change_percent.toFixed(2)}%</span>
                            </div>
                            <div class="row text-center">
                                <div class="col-6">
                                    <small class="text-muted">현재가</small>
                                    <div class="fw-bold">$${stock.current_price.toFixed(2)}</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">거래량</small>
                                    <div class="fw-bold">${stock.volume.toLocaleString()}</div>
                                </div>
                            </div>
                            <div class="mt-2">
                                <small class="badge bg-light text-dark">${stock.sector}</small>
                            </div>
                        </div>
                    </div>
                `;
                container.appendChild(card);
            });
        }

        // 상위 상승 종목 로드
        function loadTopGainers() {
            const container = document.getElementById('topGainers');
            container.innerHTML = '';
            
            topGainersData.forEach((stock, index) => {
                const item = document.createElement('div');
                item.className = 'border-bottom pb-2 mb-2';
                item.style.cursor = 'pointer';
                item.onclick = () => showStockDetail(stock.symbol);
                item.innerHTML = `
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${stock.symbol}</strong>
                            <div class="small text-muted">${stock.company_name}</div>
                        </div>
                        <div class="text-end">
                            <div class="gain-positive">+${stock.change_percent.toFixed(2)}%</div>
                            <div class="small">$${stock.current_price.toFixed(2)}</div>
                        </div>
                    </div>
                `;
                container.appendChild(item);
            });
        }

        // 종목 상세 정보 표시
        function showStockDetail(symbol) {
            currentSymbol = symbol;
            const stock = mockStocks.find(s => s.symbol === symbol) || topGainersData.find(s => s.symbol === symbol);
            
            if (!stock) return;
            
            document.getElementById('modalTitle').textContent = `${symbol} 상세 정보`;
            
            // 종목 정보 표시
            document.getElementById('stockInfo').innerHTML = `
                <h6>${symbol} - ${stock.company_name}</h6>
                <table class="table table-sm">
                    <tr><td>현재가</td><td class="fw-bold">$${stock.current_price.toFixed(2)}</td></tr>
                    <tr><td>시가</td><td>$${stock.open_price.toFixed(2)}</td></tr>
                    <tr><td>변동률</td><td class="gain-positive">+${stock.change_percent.toFixed(2)}%</td></tr>
                    <tr><td>거래량</td><td>${stock.volume.toLocaleString()}</td></tr>
                    <tr><td>섹터</td><td>${stock.sector}</td></tr>
                    <tr><td>52주 고가</td><td>$${(stock.current_price * 1.5).toFixed(2)}</td></tr>
                    <tr><td>52주 저가</td><td>$${(stock.current_price * 0.4).toFixed(2)}</td></tr>
                </table>
            `;
            
            const modal = new bootstrap.Modal(document.getElementById('stockModal'));
            modal.show();
            
            // 차트 로드
            loadChart('daily');
        }

        // 차트 로드
        function loadChart(type = 'daily') {
            if (!currentSymbol) return;
            
            const stock = mockStocks.find(s => s.symbol === currentSymbol) || topGainersData.find(s => s.symbol === currentSymbol);
            if (!stock) return;
            
            // 모의 차트 데이터 생성
            const chartData = generateChartData(stock, type);
            
            if (type === 'daily') {
                // 일봉 캔들스틱 차트
                const trace = {
                    x: chartData.dates,
                    close: chartData.close,
                    decreasing: {line: {color: '#ef5350'}},
                    high: chartData.high,
                    increasing: {line: {color: '#26a69a'}},
                    line: {color: 'rgba(31,119,180,1)'},
                    low: chartData.low,
                    open: chartData.open,
                    type: 'candlestick',
                    xaxis: 'x',
                    yaxis: 'y'
                };
                
                const layout = {
                    title: `${currentSymbol} 일봉 차트`,
                    xaxis: { title: '날짜' },
                    yaxis: { title: '가격 ($)' },
                    height: 400
                };
                
                Plotly.newPlot('stockChart', [trace], layout);
            } else {
                // 일중 라인 차트
                const trace = {
                    x: chartData.times,
                    y: chartData.prices,
                    type: 'scatter',
                    mode: 'lines',
                    line: { color: '#2196F3', width: 2 },
                    fill: 'tonexty',
                    fillcolor: 'rgba(33, 150, 243, 0.1)'
                };
                
                const layout = {
                    title: `${currentSymbol} 일중 차트`,
                    xaxis: { title: '시간' },
                    yaxis: { title: '가격 ($)' },
                    height: 400
                };
                
                Plotly.newPlot('stockChart', [trace], layout);
            }
        }

        // 차트 데이터 생성
        function generateChartData(stock, type) {
            if (type === 'daily') {
                // 30일 일봉 데이터
                const dates = [];
                const open = [];
                const high = [];
                const low = [];
                const close = [];
                
                let currentPrice = stock.open_price * 0.8; // 30일 전 가격
                
                for (let i = 30; i >= 0; i--) {
                    const date = new Date();
                    date.setDate(date.getDate() - i);
                    dates.push(date.toISOString().split('T')[0]);
                    
                    const dailyChange = (Math.random() - 0.5) * 0.1; // ±5% 변동
                    const openPrice = currentPrice;
                    const closePrice = openPrice * (1 + dailyChange);
                    const highPrice = Math.max(openPrice, closePrice) * (1 + Math.random() * 0.03);
                    const lowPrice = Math.min(openPrice, closePrice) * (1 - Math.random() * 0.03);
                    
                    open.push(openPrice);
                    high.push(highPrice);
                    low.push(lowPrice);
                    close.push(closePrice);
                    
                    currentPrice = closePrice;
                }
                
                // 마지막 날은 실제 데이터로 설정
                close[close.length - 1] = stock.current_price;
                open[open.length - 1] = stock.open_price;
                high[high.length - 1] = Math.max(stock.open_price, stock.current_price) * 1.02;
                low[low.length - 1] = Math.min(stock.open_price, stock.current_price) * 0.98;
                
                return { dates, open, high, low, close };
            } else {
                // 일중 데이터 (5분 간격)
                const times = [];
                const prices = [];
                
                const startTime = new Date();
                startTime.setHours(9, 30, 0, 0); // 9:30 AM
                
                let currentPrice = stock.open_price;
                
                for (let i = 0; i < 78; i++) { // 6.5시간 * 12 (5분 간격)
                    const time = new Date(startTime.getTime() + i * 5 * 60 * 1000);
                    times.push(time.toLocaleTimeString());
                    
                    const change = (Math.random() - 0.5) * 0.02; // ±1% 변동
                    currentPrice *= (1 + change);
                    prices.push(currentPrice);
                }
                
                // 마지막 가격을 현재가로 조정
                prices[prices.length - 1] = stock.current_price;
                
                return { times, prices };
            }
        }

        // 데이터 새로고침
        function refreshData() {
            // 모의 데이터 업데이트 (가격 변동 시뮬레이션)
            mockStocks.forEach(stock => {
                const change = (Math.random() - 0.5) * 0.05; // ±2.5% 변동
                stock.current_price *= (1 + change);
                stock.change_percent = ((stock.current_price - stock.open_price) / stock.open_price) * 100;
            });
            
            topGainersData.forEach(stock => {
                const change = (Math.random() - 0.5) * 0.05;
                stock.current_price *= (1 + change);
                stock.change_percent = ((stock.current_price - stock.open_price) / stock.open_price) * 100;
            });
            
            loadTopGainers();
            showNotification('데이터가 새로고침되었습니다', 'success');
        }

        // 알림 표시
        function showNotification(message, type = 'info') {
            const alertClass = {
                'success': 'alert-success',
                'error': 'alert-danger',
                'warning': 'alert-warning',
                'info': 'alert-info'
            }[type] || 'alert-info';
            
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 80px; right: 20px; z-index: 1050; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }
    </script>
</body>
</html>
