<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>미국 주식 스크리너 - 20% 이상 상승 종목</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .stock-card {
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
        }
        .stock-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .gain-positive { color: #28a745; font-weight: bold; }
        .gain-negative { color: #dc3545; font-weight: bold; }
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .chart-container {
            min-height: 400px;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
        }
        .criteria-badge {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9em;
        }
        .top-gainers {
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <!-- 헤더 -->
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="h3 mb-3">
                    <i class="fas fa-search text-primary"></i>
                    미국 주식 스크리너
                </h1>
                <p class="text-muted">개장 후 20% 이상 상승한 종목을 찾아보세요</p>
            </div>
        </div>

        <!-- 스크리닝 조건 설정 -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-filter"></i> 스크리닝 조건
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="screeningForm" class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">최소 상승률 (%)</label>
                                <input type="number" class="form-control" id="minGain" value="20" min="0" max="100" step="0.1">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">최소 거래량</label>
                                <input type="number" class="form-control" id="minVolume" value="100000" min="0" step="10000">
                            </div>
                            <div class="col-md-4 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary me-2">
                                    <i class="fas fa-search"></i> 스크리닝 실행
                                </button>
                                <button type="button" class="btn btn-outline-secondary" onclick="refreshData()">
                                    <i class="fas fa-sync-alt"></i> 새로고침
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-trophy"></i> 상위 상승 종목
                        </h6>
                    </div>
                    <div class="card-body top-gainers" id="topGainers">
                        <div class="text-center text-muted">
                            <i class="fas fa-spinner fa-spin"></i> 로딩 중...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 스크리닝 결과 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list"></i> 스크리닝 결과
                            <span id="resultCount" class="badge bg-primary ms-2">0</span>
                        </h5>
                        <div id="criteriaDisplay"></div>
                    </div>
                    <div class="card-body">
                        <div class="loading-spinner" id="loadingSpinner">
                            <i class="fas fa-spinner fa-spin fa-2x text-primary"></i>
                            <p class="mt-2">종목을 검색하고 있습니다...</p>
                        </div>
                        <div id="stockResults" class="row">
                            <div class="col-12 text-center text-muted py-5">
                                <i class="fas fa-search fa-3x mb-3"></i>
                                <p>스크리닝 조건을 설정하고 "스크리닝 실행" 버튼을 클릭하세요</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 종목 상세 모달 -->
    <div class="modal fade" id="stockModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="modalTitle">종목 상세 정보</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div id="stockInfo">
                                <div class="text-center">
                                    <i class="fas fa-spinner fa-spin"></i> 로딩 중...
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="chart-container" id="stockChart">
                                <div class="text-center">
                                    <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">차트를 로딩하고 있습니다...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="loadChart('daily')">일봉</button>
                                <button type="button" class="btn btn-outline-primary" onclick="loadChart('intraday')">일중</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentSymbol = '';
        
        // 페이지 로드 시 초기화
        document.addEventListener('DOMContentLoaded', function() {
            loadTopGainers();
        });

        // 스크리닝 폼 제출
        document.getElementById('screeningForm').addEventListener('submit', function(e) {
            e.preventDefault();
            runScreening();
        });

        // 스크리닝 실행
        async function runScreening() {
            const minGain = parseFloat(document.getElementById('minGain').value);
            const minVolume = parseInt(document.getElementById('minVolume').value);
            
            showLoading(true);
            
            try {
                const response = await fetch('/api/screen', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        min_gain_percent: minGain,
                        min_volume: minVolume
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    displayResults(result.data, result.criteria);
                    document.getElementById('resultCount').textContent = result.count;
                } else {
                    showError('스크리닝 실패: ' + result.error);
                }
            } catch (error) {
                showError('네트워크 오류: ' + error.message);
            } finally {
                showLoading(false);
            }
        }

        // 결과 표시
        function displayResults(stocks, criteria) {
            const container = document.getElementById('stockResults');
            
            if (stocks.length === 0) {
                container.innerHTML = `
                    <div class="col-12 text-center text-muted py-5">
                        <i class="fas fa-search fa-3x mb-3"></i>
                        <p>조건에 맞는 종목이 없습니다</p>
                        <small>다른 조건으로 다시 검색해보세요</small>
                    </div>
                `;
                return;
            }
            
            // 조건 표시
            document.getElementById('criteriaDisplay').innerHTML = `
                <span class="criteria-badge">
                    상승률 ≥ ${criteria.min_gain_percent}% | 거래량 ≥ ${criteria.min_volume.toLocaleString()}
                </span>
            `;
            
            container.innerHTML = '';
            
            stocks.forEach(stock => {
                const card = document.createElement('div');
                card.className = 'col-md-6 col-lg-4 mb-3';
                card.innerHTML = `
                    <div class="card stock-card h-100" onclick="showStockDetail('${stock.symbol}')">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <div>
                                    <h6 class="card-title mb-1">${stock.symbol}</h6>
                                    <small class="text-muted">${stock.company_name}</small>
                                </div>
                                <span class="badge bg-success">+${stock.change_percent.toFixed(2)}%</span>
                            </div>
                            <div class="row text-center">
                                <div class="col-6">
                                    <small class="text-muted">현재가</small>
                                    <div class="fw-bold">$${stock.current_price.toFixed(2)}</div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">거래량</small>
                                    <div class="fw-bold">${stock.volume.toLocaleString()}</div>
                                </div>
                            </div>
                            ${stock.sector ? `<div class="mt-2"><small class="badge bg-light text-dark">${stock.sector}</small></div>` : ''}
                        </div>
                    </div>
                `;
                container.appendChild(card);
            });
        }

        // 상위 상승 종목 로드
        async function loadTopGainers() {
            try {
                const response = await fetch('/api/top-gainers?limit=10');
                const result = await response.json();
                
                if (result.success) {
                    const container = document.getElementById('topGainers');
                    container.innerHTML = '';
                    
                    result.data.forEach((stock, index) => {
                        const item = document.createElement('div');
                        item.className = 'border-bottom pb-2 mb-2 cursor-pointer';
                        item.onclick = () => showStockDetail(stock.symbol);
                        item.innerHTML = `
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>${stock.symbol}</strong>
                                    <div class="small text-muted">${stock.company_name}</div>
                                </div>
                                <div class="text-end">
                                    <div class="gain-positive">+${stock.change_percent.toFixed(2)}%</div>
                                    <div class="small">$${stock.current_price.toFixed(2)}</div>
                                </div>
                            </div>
                        `;
                        container.appendChild(item);
                    });
                }
            } catch (error) {
                console.error('상위 상승 종목 로드 오류:', error);
            }
        }

        // 종목 상세 정보 표시
        async function showStockDetail(symbol) {
            currentSymbol = symbol;
            document.getElementById('modalTitle').textContent = `${symbol} 상세 정보`;
            
            const modal = new bootstrap.Modal(document.getElementById('stockModal'));
            modal.show();
            
            // 종목 정보 로드
            loadStockInfo(symbol);
            loadChart('daily');
        }

        // 종목 정보 로드
        async function loadStockInfo(symbol) {
            try {
                const response = await fetch(`/api/stock/${symbol}`);
                const result = await response.json();
                
                if (result.success) {
                    const data = result.data;
                    document.getElementById('stockInfo').innerHTML = `
                        <h6>${symbol} - ${data.company_name || symbol + ' Corp.'}</h6>
                        <table class="table table-sm">
                            <tr><td>현재가</td><td class="fw-bold">$${data.current_price.toFixed(2)}</td></tr>
                            <tr><td>시가</td><td>$${data.open_price.toFixed(2)}</td></tr>
                            <tr><td>변동률</td><td class="gain-positive">+${data.change_percent.toFixed(2)}%</td></tr>
                            <tr><td>거래량</td><td>${data.volume.toLocaleString()}</td></tr>
                            ${data.market_cap_formatted ? `<tr><td>시가총액</td><td>${data.market_cap_formatted}</td></tr>` : ''}
                            ${data.pe_ratio ? `<tr><td>PER</td><td>${data.pe_ratio}</td></tr>` : ''}
                            ${data.sector ? `<tr><td>섹터</td><td>${data.sector}</td></tr>` : ''}
                        </table>
                    `;
                }
            } catch (error) {
                console.error('종목 정보 로드 오류:', error);
            }
        }

        // 차트 로드
        async function loadChart(type = 'daily') {
            if (!currentSymbol) return;
            
            document.getElementById('stockChart').innerHTML = `
                <div class="text-center">
                    <i class="fas fa-spinner fa-spin fa-2x"></i>
                    <p class="mt-2">차트를 로딩하고 있습니다...</p>
                </div>
            `;
            
            try {
                const response = await fetch(`/api/chart/${currentSymbol}?type=${type}`);
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('stockChart').innerHTML = result.chart_html;
                } else {
                    document.getElementById('stockChart').innerHTML = `
                        <div class="text-center text-muted">
                            <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                            <p>차트를 로드할 수 없습니다</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('차트 로드 오류:', error);
            }
        }

        // 데이터 새로고침
        async function refreshData() {
            try {
                await fetch('/api/refresh');
                loadTopGainers();
                showNotification('데이터가 새로고침되었습니다', 'success');
            } catch (error) {
                showNotification('새로고침 실패', 'error');
            }
        }

        // 로딩 표시
        function showLoading(show) {
            document.getElementById('loadingSpinner').style.display = show ? 'block' : 'none';
        }

        // 오류 표시
        function showError(message) {
            document.getElementById('stockResults').innerHTML = `
                <div class="col-12 text-center text-danger py-5">
                    <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                    <p>${message}</p>
                </div>
            `;
        }

        // 알림 표시
        function showNotification(message, type = 'info') {
            const alertClass = {
                'success': 'alert-success',
                'error': 'alert-danger',
                'warning': 'alert-warning',
                'info': 'alert-info'
            }[type] || 'alert-info';
            
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }
    </script>
</body>
</html>
