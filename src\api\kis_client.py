"""
한국투자증권 Open API 클라이언트
"""
import requests
import json
import hashlib
import hmac
import base64
from datetime import datetime, timedelta
from typing import Dict, Optional, Any
from loguru import logger
import config
from src.api.mock_client import MockKISClient


class KISClient:
    """한국투자증권 Open API 클라이언트"""

    def __init__(self):
        # 데모 모드 확인
        if config.DEMO_MODE or config.USE_MOCK_DATA:
            logger.info("데모 모드로 실행 중 - 모의 클라이언트 사용")
            self._mock_client = MockKISClient()
            self._is_demo_mode = True
        else:
            self._is_demo_mode = False
            self._mock_client = None

        self.app_key = config.KIS_APP_KEY
        self.app_secret = config.KIS_APP_SECRET
        self.base_url = config.KIS_BASE_URL
        self.access_token = None
        self.token_expires_at = None
        
    def _get_headers(self, tr_id: str, custtype: str = "P") -> Dict[str, str]:
        """API 호출용 헤더 생성"""
        return {
            "Content-Type": "application/json",
            "authorization": f"Bearer {self.access_token}",
            "appkey": self.app_key,
            "appsecret": self.app_secret,
            "tr_id": tr_id,
            "custtype": custtype
        }
    
    def _get_websocket_headers(self) -> Dict[str, str]:
        """웹소켓용 헤더 생성"""
        return {
            "appkey": self.app_key,
            "appsecret": self.app_secret,
            "custtype": "P"
        }
    
    def get_access_token(self) -> bool:
        """액세스 토큰 발급"""
        if self._is_demo_mode:
            return self._mock_client.get_access_token()

        try:
            url = f"{self.base_url}/oauth2/tokenP"
            headers = {"Content-Type": "application/json"}
            data = {
                "grant_type": "client_credentials",
                "appkey": self.app_key,
                "appsecret": self.app_secret
            }

            response = requests.post(url, headers=headers, data=json.dumps(data))

            if response.status_code == 200:
                result = response.json()
                self.access_token = result["access_token"]
                expires_in = result.get("expires_in", 86400)  # 기본 24시간
                self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)
                logger.info("액세스 토큰 발급 성공")
                return True
            else:
                logger.error(f"토큰 발급 실패: {response.status_code}, {response.text}")
                return False

        except Exception as e:
            logger.error(f"토큰 발급 중 오류: {e}")
            return False
    
    def is_token_valid(self) -> bool:
        """토큰 유효성 확인"""
        if self._is_demo_mode:
            return self._mock_client.is_token_valid()

        if not self.access_token or not self.token_expires_at:
            return False
        return datetime.now() < self.token_expires_at - timedelta(minutes=5)  # 5분 여유

    def ensure_token(self) -> bool:
        """토큰 유효성 확인 및 갱신"""
        if self._is_demo_mode:
            return self._mock_client.ensure_token()

        if not self.is_token_valid():
            return self.get_access_token()
        return True
    
    def get_overseas_stock_price(self, symbol: str, exchange: str = "NAS") -> Optional[Dict]:
        """해외주식 현재가 조회"""
        if self._is_demo_mode:
            return self._mock_client.get_overseas_stock_price(symbol, exchange)

        if not self.ensure_token():
            return None

        try:
            url = f"{self.base_url}/uapi/overseas-price/v1/quotations/price"
            headers = self._get_headers("HHDFS00000300")

            params = {
                "AUTH": "",
                "EXCD": exchange,  # NAS: 나스닥, NYS: 뉴욕증권거래소
                "SYMB": symbol
            }

            response = requests.get(url, headers=headers, params=params)

            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"주가 조회 실패: {response.status_code}, {response.text}")
                return None

        except Exception as e:
            logger.error(f"주가 조회 중 오류: {e}")
            return None
    
    def get_overseas_stock_chart(self, symbol: str, exchange: str = "NAS", 
                                period: str = "D", count: int = 100) -> Optional[Dict]:
        """해외주식 차트 데이터 조회"""
        if not self.ensure_token():
            return None
            
        try:
            url = f"{self.base_url}/uapi/overseas-price/v1/quotations/dailyprice"
            headers = self._get_headers("HHDFS76240000")
            
            params = {
                "AUTH": "",
                "EXCD": exchange,
                "SYMB": symbol,
                "GUBN": period,  # D: 일봉, W: 주봉, M: 월봉
                "BYMD": "",
                "MODP": "1"
            }
            
            response = requests.get(url, headers=headers, params=params)
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"차트 데이터 조회 실패: {response.status_code}, {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"차트 데이터 조회 중 오류: {e}")
            return None

    def place_overseas_order(self, symbol: str, exchange: str, order_type: str,
                           quantity: int, price: float = 0) -> Optional[Dict]:
        """해외주식 주문"""
        if self._is_demo_mode:
            return self._mock_client.place_overseas_order(symbol, exchange, order_type, quantity, price)

        if not self.ensure_token():
            return None

        try:
            # 매수: TTTT1002U, 매도: TTTT1006U
            tr_id = "TTTT1002U" if order_type.upper() == "BUY" else "TTTT1006U"
            url = f"{self.base_url}/uapi/overseas-stock/v1/trading/order"
            headers = self._get_headers(tr_id)

            data = {
                "CANO": config.TRADING_CONFIG['account_number'],
                "ACNT_PRDT_CD": config.TRADING_CONFIG['account_product_code'],
                "OVRS_EXCG_CD": exchange,
                "PDNO": symbol,
                "ORD_QTY": str(quantity),
                "OVRS_ORD_UNPR": str(price) if price > 0 else "0",
                "ORD_SVR_DVSN_CD": "0",  # 0: 지정가, 1: 시장가
                "ORD_DVSN": "00" if price > 0 else "01"  # 00: 지정가, 01: 시장가
            }

            response = requests.post(url, headers=headers, data=json.dumps(data))

            if response.status_code == 200:
                result = response.json()
                if result.get("rt_cd") == "0":
                    logger.info(f"주문 성공: {symbol} {order_type} {quantity}주")
                    return result
                else:
                    logger.error(f"주문 실패: {result.get('msg1')}")
                    return None
            else:
                logger.error(f"주문 요청 실패: {response.status_code}, {response.text}")
                return None

        except Exception as e:
            logger.error(f"주문 중 오류: {e}")
            return None

    def get_overseas_balance(self) -> Optional[Dict]:
        """해외주식 잔고 조회"""
        if not self.ensure_token():
            return None

        try:
            url = f"{self.base_url}/uapi/overseas-stock/v1/trading/inquire-balance"
            headers = self._get_headers("TTTS3012R")

            params = {
                "CANO": config.TRADING_CONFIG['account_number'],
                "ACNT_PRDT_CD": config.TRADING_CONFIG['account_product_code'],
                "OVRS_EXCG_CD": "NASD",  # 나스닥
                "TR_CRCY_CD": "USD",
                "CTX_AREA_FK200": "",
                "CTX_AREA_NK200": ""
            }

            response = requests.get(url, headers=headers, params=params)

            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"잔고 조회 실패: {response.status_code}, {response.text}")
                return None

        except Exception as e:
            logger.error(f"잔고 조회 중 오류: {e}")
            return None

    def cancel_overseas_order(self, order_no: str, org_order_no: str,
                            symbol: str, exchange: str, quantity: int) -> Optional[Dict]:
        """해외주식 주문 취소"""
        if not self.ensure_token():
            return None

        try:
            url = f"{self.base_url}/uapi/overseas-stock/v1/trading/order-rvsecncl"
            headers = self._get_headers("TTTT1004U")

            data = {
                "CANO": config.TRADING_CONFIG['account_number'],
                "ACNT_PRDT_CD": config.TRADING_CONFIG['account_product_code'],
                "OVRS_EXCG_CD": exchange,
                "PDNO": symbol,
                "ORGN_ODNO": org_order_no,
                "ORD_QTY": str(quantity),
                "RVSE_CNCL_DVSN_CD": "02"  # 02: 취소
            }

            response = requests.post(url, headers=headers, data=json.dumps(data))

            if response.status_code == 200:
                result = response.json()
                if result.get("rt_cd") == "0":
                    logger.info(f"주문 취소 성공: {symbol}")
                    return result
                else:
                    logger.error(f"주문 취소 실패: {result.get('msg1')}")
                    return None
            else:
                logger.error(f"주문 취소 요청 실패: {response.status_code}, {response.text}")
                return None

        except Exception as e:
            logger.error(f"주문 취소 중 오류: {e}")
            return None
