# 한국투자증권 Open API 해외주식 자동매매 프로그램

## 개요
한국투자증권 Open API를 활용한 해외주식 자동매매 프로그램입니다.

## 거래 전략
1. 개장 후 2번째 5분 K선이 일 시가보다 높을 때
2. 당일 최고가(장전 포함) 돌파 시
3. 1분 거래량이 30만 달러 이상일 때 매수
4. 손절: 5% 손실 시
5. 익절: 10% 수익 시 절반 매도, 30% 수익 시 절반 매도, 100% 수익 시 전량 매도

## 설치 및 설정

### 1. 의존성 설치
```bash
pip install -r requirements.txt
```

### 2. 환경 변수 설정
`.env.example` 파일을 `.env`로 복사하고 API 키를 설정하세요.

### 3. 실행
```bash
python main.py
```

## 프로젝트 구조
```
├── src/
│   ├── api/          # 한국투자증권 API 클라이언트
│   ├── data/         # 시장 데이터 수집
│   ├── strategy/     # 거래 전략 엔진
│   └── trading/      # 주문 관리 시스템
├── tests/            # 테스트 코드
├── logs/             # 로그 파일
├── config.py         # 설정 파일
└── main.py           # 메인 실행 파일
```

## 주의사항
- 실제 거래 전 반드시 백테스팅을 통해 전략을 검증하세요
- API 키는 절대 공개하지 마세요
- 투자에 따른 손실은 본인 책임입니다
