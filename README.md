# 한국투자증권 Open API 해외주식 자동매매 프로그램

## 개요
한국투자증권 Open API를 활용한 해외주식 자동매매 프로그램입니다.
**웹 브라우저에서 실시간 대시보드를 통해 모니터링 및 제어가 가능합니다.**

## 🎯 거래 전략
1. 개장 후 2번째 5분 K선이 일 시가보다 높을 때
2. 당일 최고가(장전 포함) 돌파 시
3. 1분 거래량이 30만 달러 이상일 때 매수
4. 손절: 5% 손실 시
5. 익절: 10% 수익 시 절반 매도, 30% 수익 시 절반 매도, 100% 수익 시 전량 매도

## 🚀 빠른 시작 (데모 모드)

### 1. 의존성 설치
```bash
pip install -r requirements.txt
```

### 2. 웹 애플리케이션 실행
```bash
python start_web.py
```

### 3. 브라우저에서 접속
```
http://localhost:5000
```

## 📊 웹 대시보드 기능
- **실시간 포트폴리오 모니터링**: 현재 포지션, 손익 현황
- **자동매매 제어**: 거래 시작/중지 버튼
- **백테스팅**: 과거 데이터로 전략 검증
- **거래 내역**: 실시간 주문 체결 현황
- **시장 데이터**: 주요 종목 실시간 가격

## 🔧 상세 설정

### 환경 변수 설정
`.env.example` 파일을 `.env`로 복사하고 설정을 확인하세요.

**데모 모드 (기본값)**:
```bash
DEMO_MODE=true
USE_MOCK_DATA=true
KIS_APP_KEY=DEMO_APP_KEY_12345678
KIS_APP_SECRET=DEMO_APP_SECRET_ABCDEFGH
```

### 실제 거래 모드
실제 한국투자증권 API를 사용하려면:
```bash
DEMO_MODE=false
USE_MOCK_DATA=false
KIS_APP_KEY=your_real_app_key
KIS_APP_SECRET=your_real_app_secret
ACCOUNT_NUMBER=your_account_number
```

## 💻 실행 방법

### 웹 대시보드 (권장)
```bash
python start_web.py
```

### 콘솔 모드
```bash
# 실제 거래 모드
python main.py

# 백테스트 모드
python main.py backtest
```

### 테스트 실행
```bash
python run_tests.py
```

## 📁 프로젝트 구조
```
├── src/
│   ├── api/          # 한국투자증권 API 클라이언트 + 모의 클라이언트
│   ├── data/         # 시장 데이터 수집
│   ├── strategy/     # 거래 전략 엔진
│   ├── trading/      # 주문 관리 시스템
│   └── testing/      # 백테스팅 모듈
├── templates/        # 웹 템플릿
├── static/           # 정적 파일 (CSS, JS)
├── tests/            # 테스트 코드
├── logs/             # 로그 파일
├── web_app.py        # 웹 애플리케이션
├── start_web.py      # 웹 서버 시작 스크립트
├── config.py         # 설정 파일
└── main.py           # 콘솔 모드 실행 파일
```

## 🔑 테스트용 계정 정보

**데모 API 키 (이미 설정됨)**:
- APP_KEY: `DEMO_APP_KEY_12345678`
- APP_SECRET: `DEMO_APP_SECRET_ABCDEFGH`
- 계좌번호: `12345678-01`

**모의 거래 환경**:
- 초기 자본: $50,000
- 지원 종목: AAPL, TSLA, NVDA, MSFT, GOOGL, AMZN, META, NFLX
- 실시간 가격 시뮬레이션
- 주문 체결 시뮬레이션 (95% 성공률)

## 🎮 사용법

### 1. 웹 대시보드 접속
1. `python start_web.py` 실행
2. 브라우저에서 `http://localhost:5000` 접속
3. 대시보드에서 실시간 데이터 확인

### 2. 자동매매 시작
1. 웹 대시보드 우상단 "거래 시작" 버튼 클릭
2. 시스템이 자동으로 거래 신호 분석 시작
3. 조건 만족 시 자동 주문 실행

### 3. 백테스팅 실행
1. 대시보드 우측 백테스팅 패널에서 설정
2. 종목, 기간, 초기 자본 입력
3. "백테스트 실행" 버튼 클릭
4. 결과 확인

## 📈 성과 모니터링
- **실시간 포트폴리오**: 총 가치, 손익, 보유 종목 수
- **포지션 현황**: 종목별 수량, 평균단가, 현재 손익
- **거래 내역**: 최근 주문 체결 기록
- **시장 데이터**: 주요 종목 실시간 가격 변동

## ⚠️ 주의사항
- **현재 데모 모드로 실행됩니다** - 실제 거래 없음
- 실제 거래 전 반드시 백테스팅을 통해 전략을 검증하세요
- API 키는 절대 공개하지 마세요
- 투자에 따른 손실은 본인 책임입니다
- 모의 데이터는 실제 시장과 다를 수 있습니다

## 🛠️ 문제 해결

### 웹 서버가 시작되지 않는 경우
```bash
# 의존성 재설치
pip install -r requirements.txt

# 포트 충돌 확인
netstat -an | findstr :5000
```

### 데이터가 표시되지 않는 경우
1. 브라우저 새로고침 (F5)
2. 개발자 도구에서 콘솔 오류 확인
3. 로그 파일 확인: `logs/trading.log`

## 📞 지원
문제가 발생하면 다음을 확인하세요:
1. `logs/trading.log` 파일의 오류 메시지
2. 브라우저 개발자 도구의 콘솔 오류
3. 환경 변수 설정 (.env 파일)
