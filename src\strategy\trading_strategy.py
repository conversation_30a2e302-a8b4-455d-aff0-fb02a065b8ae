"""
거래 전략 엔진
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from loguru import logger
from dataclasses import dataclass
from enum import Enum
import config
from src.data.market_data import MarketDataCollector


class SignalType(Enum):
    """신호 타입"""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"


@dataclass
class TradingSignal:
    """거래 신호"""
    signal_type: SignalType
    symbol: str
    price: float
    quantity: int
    reason: str
    timestamp: datetime
    confidence: float = 0.0


@dataclass
class Position:
    """포지션 정보"""
    symbol: str
    quantity: int
    avg_price: float
    current_price: float
    unrealized_pnl: float
    unrealized_pnl_percent: float
    entry_time: datetime


class TradingStrategy:
    """거래 전략 엔진"""
    
    def __init__(self):
        self.market_data = MarketDataCollector()
        self.positions: Dict[str, Position] = {}
        self.daily_open_prices: Dict[str, float] = {}
        self.premarket_highs: Dict[str, float] = {}
        self.kline_cache: Dict[str, List[Dict]] = {}
        
    def analyze_entry_conditions(self, symbol: str, exchange: str = "NAS") -> Optional[TradingSignal]:
        """진입 조건 분석"""
        try:
            # 1. 기본 데이터 수집
            current_price_data = self.market_data.get_current_price(symbol, exchange)
            if not current_price_data:
                return None
            
            current_price = current_price_data['price']
            daily_open = current_price_data['open']
            
            # 2. 5분 K선 데이터 수집
            klines_5m = self.market_data.get_5min_klines(symbol, exchange)
            if not klines_5m or len(klines_5m) < 2:
                return None
            
            # 3. 조건 1: 개장 후 2번째 5분 K선이 일 시가보다 높은지 확인
            second_5m_kline = klines_5m[1] if len(klines_5m) > 1 else None
            if not second_5m_kline:
                return None
            
            condition1 = second_5m_kline['close'] > daily_open
            logger.info(f"{symbol} 조건1 (2번째 5분봉 > 일시가): {condition1}")
            
            # 4. 조건 2: 당일 최고가(장전 포함) 돌파 확인
            premarket_high = self.market_data.get_premarket_high(symbol, exchange)
            daily_high = current_price_data['high']
            
            # 장전 최고가와 당일 최고가 중 더 높은 값
            today_high = max(premarket_high or 0, daily_high)
            condition2 = current_price > today_high
            logger.info(f"{symbol} 조건2 (현재가 > 당일최고가): {condition2} ({current_price} > {today_high})")
            
            # 5. 조건 3: 1분 거래량이 30만 달러 이상인지 확인
            volume_1min_usd = self.market_data.calculate_1min_volume_usd(symbol, exchange)
            condition3 = volume_1min_usd and volume_1min_usd >= config.STRATEGY_CONFIG['volume_threshold']
            logger.info(f"{symbol} 조건3 (1분거래량 >= 30만USD): {condition3} ({volume_1min_usd})")
            
            # 6. 모든 조건 만족 시 매수 신호 생성
            if condition1 and condition2 and condition3:
                # 포지션 크기 계산
                max_position_usd = config.TRADING_CONFIG['max_position_size']
                quantity = int(max_position_usd / current_price)
                
                return TradingSignal(
                    signal_type=SignalType.BUY,
                    symbol=symbol,
                    price=current_price,
                    quantity=quantity,
                    reason=f"모든 진입 조건 만족: 2번째5분봉({second_5m_kline['close']:.2f}) > 일시가({daily_open:.2f}), "
                           f"현재가({current_price:.2f}) > 당일최고가({today_high:.2f}), "
                           f"1분거래량({volume_1min_usd:.0f}) >= 30만USD",
                    timestamp=datetime.now(),
                    confidence=0.8
                )
            
            return TradingSignal(
                signal_type=SignalType.HOLD,
                symbol=symbol,
                price=current_price,
                quantity=0,
                reason=f"진입 조건 미충족: 조건1({condition1}), 조건2({condition2}), 조건3({condition3})",
                timestamp=datetime.now(),
                confidence=0.0
            )
            
        except Exception as e:
            logger.error(f"진입 조건 분석 오류: {e}")
            return None
    
    def analyze_exit_conditions(self, symbol: str, position: Position) -> Optional[TradingSignal]:
        """청산 조건 분석"""
        try:
            current_price_data = self.market_data.get_current_price(symbol)
            if not current_price_data:
                return None
            
            current_price = current_price_data['price']
            
            # PnL 계산
            pnl_percent = (current_price - position.avg_price) / position.avg_price
            
            # 손절 조건: 5% 손실
            if pnl_percent <= -config.TRADING_CONFIG['stop_loss_percent']:
                return TradingSignal(
                    signal_type=SignalType.SELL,
                    symbol=symbol,
                    price=current_price,
                    quantity=position.quantity,
                    reason=f"손절: {pnl_percent:.2%} 손실",
                    timestamp=datetime.now(),
                    confidence=1.0
                )
            
            # 익절 조건들
            take_profit_levels = config.TRADING_CONFIG['take_profit_levels']
            take_profit_ratios = config.TRADING_CONFIG['take_profit_ratios']
            
            for i, (level, ratio) in enumerate(zip(take_profit_levels, take_profit_ratios)):
                if pnl_percent >= level:
                    sell_quantity = int(position.quantity * ratio)
                    
                    if i < 2:  # 10%, 30% 익절 시 부분 매도
                        return TradingSignal(
                            signal_type=SignalType.SELL,
                            symbol=symbol,
                            price=current_price,
                            quantity=sell_quantity,
                            reason=f"익절 {level:.0%}: {pnl_percent:.2%} 수익, {ratio:.0%} 매도",
                            timestamp=datetime.now(),
                            confidence=0.9
                        )
                    else:  # 100% 익절 시 전량 매도
                        return TradingSignal(
                            signal_type=SignalType.SELL,
                            symbol=symbol,
                            price=current_price,
                            quantity=position.quantity,
                            reason=f"익절 {level:.0%}: {pnl_percent:.2%} 수익, 전량 매도",
                            timestamp=datetime.now(),
                            confidence=1.0
                        )
            
            return TradingSignal(
                signal_type=SignalType.HOLD,
                symbol=symbol,
                price=current_price,
                quantity=0,
                reason=f"보유: {pnl_percent:.2%} PnL",
                timestamp=datetime.now(),
                confidence=0.0
            )
            
        except Exception as e:
            logger.error(f"청산 조건 분석 오류: {e}")
            return None
    
    def update_position(self, symbol: str, quantity: int, price: float, action: str):
        """포지션 업데이트"""
        try:
            if symbol not in self.positions:
                if action == "BUY" and quantity > 0:
                    self.positions[symbol] = Position(
                        symbol=symbol,
                        quantity=quantity,
                        avg_price=price,
                        current_price=price,
                        unrealized_pnl=0.0,
                        unrealized_pnl_percent=0.0,
                        entry_time=datetime.now()
                    )
                    logger.info(f"새 포지션 생성: {symbol} {quantity}주 @ ${price:.2f}")
            else:
                position = self.positions[symbol]
                
                if action == "BUY":
                    # 추가 매수
                    total_cost = (position.quantity * position.avg_price) + (quantity * price)
                    total_quantity = position.quantity + quantity
                    position.avg_price = total_cost / total_quantity
                    position.quantity = total_quantity
                    logger.info(f"포지션 추가: {symbol} +{quantity}주, 총 {total_quantity}주 @ ${position.avg_price:.2f}")
                    
                elif action == "SELL":
                    # 매도
                    position.quantity -= quantity
                    if position.quantity <= 0:
                        # 포지션 완전 청산
                        del self.positions[symbol]
                        logger.info(f"포지션 청산: {symbol}")
                    else:
                        logger.info(f"부분 매도: {symbol} -{quantity}주, 잔여 {position.quantity}주")
                        
        except Exception as e:
            logger.error(f"포지션 업데이트 오류: {e}")
    
    def get_current_positions(self) -> Dict[str, Position]:
        """현재 포지션 조회"""
        try:
            # 포지션별 현재가 및 PnL 업데이트
            for symbol, position in self.positions.items():
                current_price_data = self.market_data.get_current_price(symbol)
                if current_price_data:
                    current_price = current_price_data['price']
                    position.current_price = current_price
                    position.unrealized_pnl = (current_price - position.avg_price) * position.quantity
                    position.unrealized_pnl_percent = (current_price - position.avg_price) / position.avg_price
            
            return self.positions.copy()
            
        except Exception as e:
            logger.error(f"포지션 조회 오류: {e}")
            return {}
    
    def should_trade_symbol(self, symbol: str) -> bool:
        """해당 종목 거래 가능 여부 확인"""
        try:
            # 시장 개장 시간 확인
            if not self.market_data.is_market_open():
                return False
            
            # 이미 포지션이 있는 경우 추가 매수 제한
            if symbol in self.positions:
                return False
            
            # 기타 필터링 조건들...
            return True
            
        except Exception as e:
            logger.error(f"거래 가능 여부 확인 오류: {e}")
            return False
