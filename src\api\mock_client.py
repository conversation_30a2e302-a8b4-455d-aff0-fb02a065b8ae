"""
모의 API 클라이언트 (데모용)
"""
import random
import time
from datetime import datetime, timedelta
from typing import Dict, Optional, Any
from loguru import logger
import numpy as np


class MockKISClient:
    """모의 한국투자증권 API 클라이언트"""
    
    def __init__(self):
        self.access_token = "MOCK_ACCESS_TOKEN_123456"
        self.token_expires_at = datetime.now() + timedelta(hours=24)
        self.mock_prices = {
            "AAPL": 175.50,
            "TSLA": 245.30,
            "NVDA": 485.20,
            "MSFT": 378.90,
            "GOOGL": 142.80,
            "AMZN": 155.40,
            "META": 325.60,
            "NFLX": 445.70
        }
        self.mock_orders = []
        self.order_counter = 0
        
        logger.info("모의 API 클라이언트 초기화 완료")
    
    def get_access_token(self) -> bool:
        """모의 액세스 토큰 발급"""
        logger.info("모의 토큰 발급 성공")
        return True
    
    def is_token_valid(self) -> bool:
        """모의 토큰 유효성 확인"""
        return datetime.now() < self.token_expires_at
    
    def ensure_token(self) -> bool:
        """모의 토큰 유효성 확인 및 갱신"""
        return True
    
    def get_overseas_stock_price(self, symbol: str, exchange: str = "NAS") -> Optional[Dict]:
        """모의 해외주식 현재가 조회"""
        try:
            if symbol not in self.mock_prices:
                logger.warning(f"지원하지 않는 종목: {symbol}")
                return None
            
            base_price = self.mock_prices[symbol]
            
            # 가격 변동 시뮬레이션 (±2% 범위)
            change_percent = random.uniform(-0.02, 0.02)
            current_price = base_price * (1 + change_percent)
            
            # 일중 고가/저가 시뮬레이션
            high_price = current_price * random.uniform(1.0, 1.015)
            low_price = current_price * random.uniform(0.985, 1.0)
            open_price = current_price * random.uniform(0.995, 1.005)
            prev_close = base_price
            
            # 거래량 시뮬레이션
            volume = random.randint(1000000, 50000000)
            
            # 가격 업데이트 (다음 호출을 위해)
            self.mock_prices[symbol] = current_price
            
            return {
                "rt_cd": "0",
                "output": {
                    "last": str(current_price),
                    "tvol": str(volume),
                    "diff": str(current_price - prev_close),
                    "rate": str(change_percent * 100),
                    "high": str(high_price),
                    "low": str(low_price),
                    "open": str(open_price),
                    "base": str(prev_close)
                }
            }
            
        except Exception as e:
            logger.error(f"모의 주가 조회 오류: {e}")
            return None
    
    def get_overseas_stock_chart(self, symbol: str, exchange: str = "NAS", 
                                period: str = "D", count: int = 100) -> Optional[Dict]:
        """모의 해외주식 차트 데이터 조회"""
        try:
            if symbol not in self.mock_prices:
                return None
            
            base_price = self.mock_prices[symbol]
            output2 = []
            
            # 과거 데이터 생성
            for i in range(count):
                date = datetime.now() - timedelta(days=count - i)
                
                # 가격 변동 시뮬레이션
                daily_change = random.uniform(-0.03, 0.03)
                close_price = base_price * (1 + daily_change * (i / count))
                
                open_price = close_price * random.uniform(0.98, 1.02)
                high_price = max(open_price, close_price) * random.uniform(1.0, 1.02)
                low_price = min(open_price, close_price) * random.uniform(0.98, 1.0)
                
                volume = random.randint(500000, 20000000)
                amount = volume * close_price
                
                output2.append({
                    "xymd": date.strftime("%Y%m%d"),
                    "open": str(round(open_price, 2)),
                    "high": str(round(high_price, 2)),
                    "low": str(round(low_price, 2)),
                    "clos": str(round(close_price, 2)),
                    "tvol": str(volume),
                    "tamt": str(round(amount, 2))
                })
            
            return {
                "rt_cd": "0",
                "output2": output2
            }
            
        except Exception as e:
            logger.error(f"모의 차트 데이터 조회 오류: {e}")
            return None
    
    def place_overseas_order(self, symbol: str, exchange: str, order_type: str, 
                           quantity: int, price: float = 0) -> Optional[Dict]:
        """모의 해외주식 주문"""
        try:
            self.order_counter += 1
            order_id = f"MOCK_ORDER_{self.order_counter:06d}"
            
            # 주문 성공 시뮬레이션 (95% 성공률)
            if random.random() < 0.95:
                self.mock_orders.append({
                    "order_id": order_id,
                    "symbol": symbol,
                    "side": order_type,
                    "quantity": quantity,
                    "price": price,
                    "status": "FILLED",
                    "timestamp": datetime.now()
                })
                
                logger.info(f"모의 주문 체결: {symbol} {order_type} {quantity}주 @ ${price:.2f}")
                
                return {
                    "rt_cd": "0",
                    "output": {
                        "ODNO": order_id,
                        "ORD_TMD": datetime.now().strftime("%H%M%S")
                    },
                    "msg1": "주문이 정상적으로 접수되었습니다."
                }
            else:
                logger.warning(f"모의 주문 실패: {symbol} {order_type}")
                return {
                    "rt_cd": "1",
                    "msg1": "주문 처리 중 오류가 발생했습니다."
                }
                
        except Exception as e:
            logger.error(f"모의 주문 오류: {e}")
            return None
    
    def get_overseas_balance(self) -> Optional[Dict]:
        """모의 해외주식 잔고 조회"""
        try:
            # 모의 잔고 데이터
            mock_balance = {
                "rt_cd": "0",
                "output1": [
                    {
                        "cano": "12345678",
                        "acnt_prdt_cd": "01",
                        "pdno": "AAPL",
                        "prdt_name": "Apple Inc",
                        "trad_dvsn_name": "현금",
                        "bfdy_buy_qty": "100",
                        "bfdy_sll_qty": "0",
                        "thdt_buyqty": "0",
                        "thdt_sll_qty": "0",
                        "hldg_qty": "100",
                        "ord_psbl_qty": "100",
                        "pchs_avg_pric": "170.50",
                        "pchs_amt": "17050.00",
                        "prpr": "175.50",
                        "evlu_amt": "17550.00",
                        "evlu_pfls_amt": "500.00",
                        "evlu_pfls_rt": "2.93",
                        "evlu_erng_rt": "2.93"
                    }
                ],
                "output2": [
                    {
                        "dnca_tot_amt": "50000.00",
                        "nxdy_excc_amt": "50000.00",
                        "prvs_rcdl_excc_amt": "0.00",
                        "cma_evlu_amt": "50000.00",
                        "bfdy_buy_amt": "17050.00",
                        "thdt_buy_amt": "0.00",
                        "nxdy_auto_rdpt_amt": "0.00",
                        "bfdy_sll_amt": "0.00",
                        "thdt_sll_amt": "0.00",
                        "d2_auto_rdpt_amt": "0.00",
                        "bfdy_tlex_amt": "0.00",
                        "thdt_tlex_amt": "0.00",
                        "tot_loan_amt": "0.00",
                        "scts_evlu_amt": "17550.00",
                        "tot_evlu_amt": "67550.00",
                        "nass_amt": "67550.00",
                        "fncg_gld_auto_rdpt_yn": "N",
                        "pchs_amt_smtl_amt": "17050.00",
                        "evlu_amt_smtl_amt": "17550.00",
                        "evlu_pfls_smtl_amt": "500.00",
                        "tot_stln_slng_chgs": "0.00",
                        "bfdy_tot_asst_evlu_amt": "67050.00",
                        "asst_icdc_amt": "500.00",
                        "asst_icdc_erng_rt": "0.75"
                    }
                ]
            }
            
            return mock_balance
            
        except Exception as e:
            logger.error(f"모의 잔고 조회 오류: {e}")
            return None
    
    def cancel_overseas_order(self, order_no: str, org_order_no: str, 
                            symbol: str, exchange: str, quantity: int) -> Optional[Dict]:
        """모의 해외주식 주문 취소"""
        try:
            logger.info(f"모의 주문 취소: {symbol}")
            
            return {
                "rt_cd": "0",
                "output": {
                    "ODNO": order_no,
                    "ORD_TMD": datetime.now().strftime("%H%M%S")
                },
                "msg1": "주문이 정상적으로 취소되었습니다."
            }
            
        except Exception as e:
            logger.error(f"모의 주문 취소 오류: {e}")
            return None
    
    def get_mock_orders(self) -> list:
        """모의 주문 내역 조회"""
        return self.mock_orders.copy()
    
    def reset_mock_data(self):
        """모의 데이터 리셋"""
        self.mock_orders.clear()
        self.order_counter = 0
        
        # 가격 리셋
        self.mock_prices = {
            "AAPL": 175.50,
            "TSLA": 245.30,
            "NVDA": 485.20,
            "MSFT": 378.90,
            "GOOGL": 142.80,
            "AMZN": 155.40,
            "META": 325.60,
            "NFLX": 445.70
        }
        
        logger.info("모의 데이터가 리셋되었습니다")
