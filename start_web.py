#!/usr/bin/env python3
"""
웹 애플리케이션 시작 스크립트
"""
import os
import sys
import subprocess
from pathlib import Path

def check_dependencies():
    """의존성 확인"""
    try:
        import flask
        import pandas
        import numpy
        import loguru
        print("✅ 모든 의존성이 설치되어 있습니다.")
        return True
    except ImportError as e:
        print(f"❌ 의존성 누락: {e}")
        print("다음 명령어로 의존성을 설치하세요:")
        print("pip install -r requirements.txt")
        return False

def check_env_file():
    """환경 변수 파일 확인"""
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env 파일이 없습니다.")
        print("다음 명령어로 환경 파일을 생성하세요:")
        print("cp .env.example .env")
        return False
    
    print("✅ 환경 변수 파일이 존재합니다.")
    return True

def create_directories():
    """필요한 디렉토리 생성"""
    directories = ["logs", "static", "static/js", "templates"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✅ 필요한 디렉토리가 생성되었습니다.")

def main():
    """메인 함수"""
    print("🚀 한국투자증권 자동매매 웹 애플리케이션 시작")
    print("=" * 50)
    
    # 의존성 확인
    if not check_dependencies():
        sys.exit(1)
    
    # 환경 파일 확인
    if not check_env_file():
        sys.exit(1)
    
    # 디렉토리 생성
    create_directories()
    
    print("\n📊 웹 대시보드 정보:")
    print("- URL: http://localhost:5000")
    print("- 데모 모드: 활성화됨")
    print("- 테스트 API 키: 설정됨")
    print("- 모의 데이터: 사용 중")
    
    print("\n🔧 주요 기능:")
    print("- 실시간 포트폴리오 모니터링")
    print("- 자동매매 시작/중지")
    print("- 백테스팅 실행")
    print("- 거래 내역 조회")
    print("- 시장 데이터 확인")
    
    print("\n⚠️  주의사항:")
    print("- 현재 데모 모드로 실행됩니다")
    print("- 실제 거래는 발생하지 않습니다")
    print("- 모든 데이터는 시뮬레이션입니다")
    
    print("\n" + "=" * 50)
    print("웹 서버를 시작합니다...")
    
    try:
        # 웹 애플리케이션 실행
        from web_app import app, socketio, initialize_components, start_real_time_updates
        
        # 컴포넌트 초기화
        if initialize_components():
            # 실시간 업데이트 시작
            start_real_time_updates()
            
            print("✅ 서버가 성공적으로 시작되었습니다!")
            print("브라우저에서 http://localhost:5000 을 열어주세요.")
            
            # Flask 서버 실행
            socketio.run(app, host='0.0.0.0', port=5000, debug=False)
        else:
            print("❌ 컴포넌트 초기화 실패")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n👋 서버가 중지되었습니다.")
    except Exception as e:
        print(f"\n❌ 서버 실행 중 오류: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
