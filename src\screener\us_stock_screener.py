"""
미국 주식 스크리너 - 개장 후 20% 이상 상승 종목 검색
"""
import pandas as pd
import numpy as np
import requests
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Tuple
from loguru import logger
from dataclasses import dataclass
import config
from src.api.kis_client import KISClient


@dataclass
class StockScreenResult:
    """스크리닝 결과"""
    symbol: str
    company_name: str
    current_price: float
    open_price: float
    change_percent: float
    volume: int
    market_cap: Optional[float] = None
    sector: Optional[str] = None
    timestamp: datetime = None


class USStockScreener:
    """미국 주식 스크리너"""
    
    def __init__(self):
        self.kis_client = KISClient()
        
        # 주요 미국 주식 심볼 리스트 (실제로는 더 많은 종목 필요)
        self.stock_universe = [
            # 대형주
            "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA", "NFLX",
            "AMD", "INTC", "CRM", "ORCL", "ADBE", "PYPL", "UBER", "LYFT",
            
            # 중형주
            "ROKU", "ZOOM", "DOCU", "SNOW", "PLTR", "COIN", "RBLX", "HOOD",
            "RIVN", "LCID", "NIO", "XPEV", "LI", "BABA", "JD", "PDD",
            
            # 소형주 및 성장주
            "SPCE", "CLOV", "WISH", "SOFI", "OPEN", "SKLZ", "DKNG", "PENN",
            "GME", "AMC", "BB", "NOK", "SNDL", "TLRY", "CGC", "ACB",
            
            # 바이오텍
            "MRNA", "BNTX", "NVAX", "GILD", "BIIB", "REGN", "VRTX", "ILMN",
            
            # 에너지
            "XOM", "CVX", "COP", "EOG", "SLB", "HAL", "OXY", "DVN",
            
            # 금융
            "JPM", "BAC", "WFC", "GS", "MS", "C", "USB", "PNC",
            
            # 소비재
            "KO", "PEP", "WMT", "TGT", "HD", "LOW", "MCD", "SBUX",
            
            # 헬스케어
            "JNJ", "PFE", "UNH", "ABBV", "TMO", "DHR", "ABT", "BMY"
        ]
        
        # 모의 데이터 (실제로는 실시간 API에서 가져옴)
        self.mock_data = self._generate_mock_data()
    
    def _generate_mock_data(self) -> Dict[str, Dict]:
        """모의 주식 데이터 생성"""
        mock_data = {}
        
        for symbol in self.stock_universe:
            # 기본 가격 설정
            base_prices = {
                "AAPL": 175.0, "MSFT": 380.0, "GOOGL": 140.0, "AMZN": 155.0,
                "TSLA": 245.0, "META": 325.0, "NVDA": 485.0, "NFLX": 445.0,
                "SPCE": 8.5, "CLOV": 3.2, "WISH": 1.8, "SOFI": 7.5,
                "GME": 18.5, "AMC": 5.2, "MRNA": 95.0, "COIN": 85.0
            }
            
            base_price = base_prices.get(symbol, np.random.uniform(10, 200))
            
            # 개장가 설정 (전일 종가 기준)
            open_price = base_price * np.random.uniform(0.95, 1.05)
            
            # 현재가 설정 (일부 종목은 20% 이상 상승하도록)
            if np.random.random() < 0.15:  # 15% 확률로 큰 상승
                current_price = open_price * np.random.uniform(1.20, 1.50)  # 20-50% 상승
            elif np.random.random() < 0.3:  # 30% 확률로 중간 상승
                current_price = open_price * np.random.uniform(1.05, 1.20)  # 5-20% 상승
            else:  # 나머지는 일반적인 변동
                current_price = open_price * np.random.uniform(0.95, 1.10)  # -5% ~ +10%
            
            change_percent = ((current_price - open_price) / open_price) * 100
            
            mock_data[symbol] = {
                'symbol': symbol,
                'current_price': round(current_price, 2),
                'open_price': round(open_price, 2),
                'change_percent': round(change_percent, 2),
                'volume': np.random.randint(100000, 10000000),
                'high': round(max(open_price, current_price) * np.random.uniform(1.0, 1.05), 2),
                'low': round(min(open_price, current_price) * np.random.uniform(0.95, 1.0), 2),
                'market_cap': np.random.uniform(1e9, 1e12),  # 10억 ~ 1조 달러
                'sector': np.random.choice(['Technology', 'Healthcare', 'Finance', 'Energy', 'Consumer'])
            }
        
        return mock_data
    
    def screen_stocks(self, min_gain_percent: float = 20.0, 
                     min_volume: int = 100000) -> List[StockScreenResult]:
        """조건에 맞는 종목 스크리닝"""
        try:
            logger.info(f"스크리닝 시작: 최소 상승률 {min_gain_percent}%, 최소 거래량 {min_volume:,}")
            
            results = []
            
            for symbol in self.stock_universe:
                try:
                    # 실제 환경에서는 API 호출
                    if config.DEMO_MODE:
                        stock_data = self.mock_data.get(symbol, {})
                        if not stock_data:
                            continue
                    else:
                        # 실제 API 호출
                        price_data = self.kis_client.get_overseas_stock_price(symbol)
                        if not price_data or price_data.get("rt_cd") != "0":
                            continue
                        
                        output = price_data.get("output", {})
                        stock_data = {
                            'current_price': float(output.get("last", 0)),
                            'open_price': float(output.get("open", 0)),
                            'volume': int(output.get("tvol", 0)),
                            'high': float(output.get("high", 0)),
                            'low': float(output.get("low", 0))
                        }
                        
                        if stock_data['open_price'] > 0:
                            stock_data['change_percent'] = ((stock_data['current_price'] - stock_data['open_price']) / stock_data['open_price']) * 100
                        else:
                            continue
                    
                    # 스크리닝 조건 확인
                    if (stock_data.get('change_percent', 0) >= min_gain_percent and 
                        stock_data.get('volume', 0) >= min_volume):
                        
                        result = StockScreenResult(
                            symbol=symbol,
                            company_name=self._get_company_name(symbol),
                            current_price=stock_data['current_price'],
                            open_price=stock_data['open_price'],
                            change_percent=stock_data['change_percent'],
                            volume=stock_data['volume'],
                            market_cap=stock_data.get('market_cap'),
                            sector=stock_data.get('sector'),
                            timestamp=datetime.now()
                        )
                        
                        results.append(result)
                        logger.info(f"조건 만족: {symbol} +{stock_data['change_percent']:.2f}%")
                
                except Exception as e:
                    logger.error(f"{symbol} 처리 중 오류: {e}")
                    continue
            
            # 상승률 순으로 정렬
            results.sort(key=lambda x: x.change_percent, reverse=True)
            
            logger.info(f"스크리닝 완료: {len(results)}개 종목 발견")
            return results
            
        except Exception as e:
            logger.error(f"스크리닝 오류: {e}")
            return []
    
    def _get_company_name(self, symbol: str) -> str:
        """회사명 조회 (간단한 매핑)"""
        company_names = {
            "AAPL": "Apple Inc.",
            "MSFT": "Microsoft Corporation",
            "GOOGL": "Alphabet Inc.",
            "AMZN": "Amazon.com Inc.",
            "TSLA": "Tesla Inc.",
            "META": "Meta Platforms Inc.",
            "NVDA": "NVIDIA Corporation",
            "NFLX": "Netflix Inc.",
            "SPCE": "Virgin Galactic Holdings",
            "CLOV": "Clover Health Investments",
            "WISH": "ContextLogic Inc.",
            "SOFI": "SoFi Technologies Inc.",
            "GME": "GameStop Corp.",
            "AMC": "AMC Entertainment Holdings",
            "MRNA": "Moderna Inc.",
            "COIN": "Coinbase Global Inc."
        }
        
        return company_names.get(symbol, f"{symbol} Corp.")
    
    def get_detailed_info(self, symbol: str) -> Optional[Dict]:
        """종목 상세 정보 조회"""
        try:
            if config.DEMO_MODE:
                base_data = self.mock_data.get(symbol, {})
                if not base_data:
                    return None
                
                # 추가 상세 정보 생성
                detailed_info = base_data.copy()
                detailed_info.update({
                    'company_name': self._get_company_name(symbol),
                    'pe_ratio': round(np.random.uniform(10, 50), 2),
                    'market_cap_formatted': f"${base_data.get('market_cap', 0)/1e9:.1f}B",
                    'avg_volume': np.random.randint(500000, 5000000),
                    '52_week_high': round(base_data['current_price'] * np.random.uniform(1.1, 2.0), 2),
                    '52_week_low': round(base_data['current_price'] * np.random.uniform(0.3, 0.9), 2),
                    'dividend_yield': round(np.random.uniform(0, 5), 2) if np.random.random() > 0.3 else 0,
                    'beta': round(np.random.uniform(0.5, 2.5), 2)
                })
                
                return detailed_info
            else:
                # 실제 API 호출로 상세 정보 조회
                price_data = self.kis_client.get_overseas_stock_price(symbol)
                if price_data and price_data.get("rt_cd") == "0":
                    return price_data.get("output", {})
                
            return None
            
        except Exception as e:
            logger.error(f"{symbol} 상세 정보 조회 오류: {e}")
            return None
    
    def get_top_gainers(self, limit: int = 10) -> List[StockScreenResult]:
        """상위 상승 종목 조회"""
        all_results = []
        
        for symbol in self.stock_universe:
            try:
                if config.DEMO_MODE:
                    stock_data = self.mock_data.get(symbol, {})
                    if not stock_data:
                        continue
                else:
                    # 실제 API 호출
                    price_data = self.kis_client.get_overseas_stock_price(symbol)
                    if not price_data or price_data.get("rt_cd") != "0":
                        continue
                    
                    output = price_data.get("output", {})
                    stock_data = {
                        'current_price': float(output.get("last", 0)),
                        'open_price': float(output.get("open", 0)),
                        'volume': int(output.get("tvol", 0))
                    }
                    
                    if stock_data['open_price'] > 0:
                        stock_data['change_percent'] = ((stock_data['current_price'] - stock_data['open_price']) / stock_data['open_price']) * 100
                    else:
                        continue
                
                if stock_data.get('change_percent', 0) > 0:  # 상승 종목만
                    result = StockScreenResult(
                        symbol=symbol,
                        company_name=self._get_company_name(symbol),
                        current_price=stock_data['current_price'],
                        open_price=stock_data['open_price'],
                        change_percent=stock_data['change_percent'],
                        volume=stock_data['volume'],
                        timestamp=datetime.now()
                    )
                    all_results.append(result)
                    
            except Exception as e:
                logger.error(f"{symbol} 처리 중 오류: {e}")
                continue
        
        # 상승률 순으로 정렬하여 상위 종목 반환
        all_results.sort(key=lambda x: x.change_percent, reverse=True)
        return all_results[:limit]
    
    def refresh_data(self):
        """데이터 새로고침"""
        if config.DEMO_MODE:
            self.mock_data = self._generate_mock_data()
            logger.info("모의 데이터가 새로고침되었습니다")
        else:
            logger.info("실시간 데이터 새로고침 완료")
