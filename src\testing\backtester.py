"""
백테스팅 모듈
"""
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from loguru import logger
import config


@dataclass
class BacktestResult:
    """백테스트 결과"""
    total_return: float
    annual_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    avg_win: float
    avg_loss: float
    equity_curve: pd.DataFrame
    trade_log: pd.DataFrame


class MockMarketData:
    """모의 시장 데이터 생성기"""
    
    def __init__(self, start_date: str, end_date: str):
        self.start_date = pd.to_datetime(start_date)
        self.end_date = pd.to_datetime(end_date)
        
    def generate_stock_data(self, symbol: str, initial_price: float = 100.0) -> pd.DataFrame:
        """주식 데이터 생성"""
        try:
            # 거래일만 생성 (주말 제외)
            date_range = pd.bdate_range(start=self.start_date, end=self.end_date)
            
            # 랜덤 워크 기반 가격 생성
            np.random.seed(42)  # 재현 가능한 결과를 위해
            returns = np.random.normal(0.0005, 0.02, len(date_range))  # 일일 수익률
            
            # 가격 계산
            prices = [initial_price]
            for ret in returns[1:]:
                prices.append(prices[-1] * (1 + ret))
            
            # OHLCV 데이터 생성
            data = []
            for i, (date, price) in enumerate(zip(date_range, prices)):
                # 일중 변동성 시뮬레이션
                daily_volatility = np.random.uniform(0.01, 0.05)
                
                open_price = price * np.random.uniform(0.98, 1.02)
                close_price = price
                high_price = max(open_price, close_price) * (1 + daily_volatility)
                low_price = min(open_price, close_price) * (1 - daily_volatility)
                volume = int(np.random.uniform(100000, 1000000))
                
                data.append({
                    'date': date,
                    'symbol': symbol,
                    'open': round(open_price, 2),
                    'high': round(high_price, 2),
                    'low': round(low_price, 2),
                    'close': round(close_price, 2),
                    'volume': volume,
                    'amount': volume * close_price
                })
            
            return pd.DataFrame(data)
            
        except Exception as e:
            logger.error(f"주식 데이터 생성 오류: {e}")
            return pd.DataFrame()
    
    def generate_5min_data(self, daily_data: pd.DataFrame) -> pd.DataFrame:
        """5분봉 데이터 생성"""
        try:
            all_5min_data = []
            
            for _, day_data in daily_data.iterrows():
                date = day_data['date']
                open_price = day_data['open']
                high_price = day_data['high']
                low_price = day_data['low']
                close_price = day_data['close']
                daily_volume = day_data['volume']
                
                # 하루를 78개의 5분봉으로 분할 (6.5시간 * 12)
                market_open = date.replace(hour=9, minute=30)
                
                for i in range(78):
                    time_5m = market_open + timedelta(minutes=5 * i)
                    
                    # 5분봉 가격 시뮬레이션
                    progress = i / 77  # 0 to 1
                    base_price = open_price + (close_price - open_price) * progress
                    
                    # 노이즈 추가
                    noise = np.random.uniform(-0.01, 0.01)
                    kline_close = base_price * (1 + noise)
                    kline_open = kline_close * np.random.uniform(0.995, 1.005)
                    
                    kline_high = max(kline_open, kline_close) * np.random.uniform(1.0, 1.01)
                    kline_low = min(kline_open, kline_close) * np.random.uniform(0.99, 1.0)
                    
                    # 범위 제한
                    kline_high = min(kline_high, high_price)
                    kline_low = max(kline_low, low_price)
                    
                    kline_volume = int(daily_volume / 78 * np.random.uniform(0.5, 2.0))
                    
                    all_5min_data.append({
                        'timestamp': time_5m,
                        'symbol': day_data['symbol'],
                        'open': round(kline_open, 2),
                        'high': round(kline_high, 2),
                        'low': round(kline_low, 2),
                        'close': round(kline_close, 2),
                        'volume': kline_volume,
                        'amount': kline_volume * kline_close
                    })
            
            return pd.DataFrame(all_5min_data)
            
        except Exception as e:
            logger.error(f"5분봉 데이터 생성 오류: {e}")
            return pd.DataFrame()


class Backtester:
    """백테스터"""
    
    def __init__(self, initial_capital: float = 10000.0):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.positions = {}
        self.trade_log = []
        self.equity_curve = []
        
    def simulate_strategy(self, symbols: List[str], start_date: str, end_date: str) -> BacktestResult:
        """전략 시뮬레이션"""
        try:
            logger.info(f"백테스트 시작: {start_date} ~ {end_date}")
            
            # 모의 데이터 생성
            mock_data = MockMarketData(start_date, end_date)
            
            # 각 심볼별 데이터 생성
            all_daily_data = {}
            all_5min_data = {}
            
            for symbol in symbols:
                daily_data = mock_data.generate_stock_data(symbol)
                if not daily_data.empty:
                    all_daily_data[symbol] = daily_data
                    all_5min_data[symbol] = mock_data.generate_5min_data(daily_data)
            
            # 백테스트 실행
            self._run_backtest(all_daily_data, all_5min_data)
            
            # 결과 계산
            result = self._calculate_results()
            
            logger.info(f"백테스트 완료: 총 수익률 {result.total_return:.2%}")
            return result
            
        except Exception as e:
            logger.error(f"백테스트 시뮬레이션 오류: {e}")
            return None
    
    def _run_backtest(self, daily_data: Dict[str, pd.DataFrame], 
                     min5_data: Dict[str, pd.DataFrame]):
        """백테스트 실행"""
        try:
            # 모든 날짜 수집
            all_dates = set()
            for df in daily_data.values():
                all_dates.update(df['date'].dt.date)
            
            all_dates = sorted(list(all_dates))
            
            for date in all_dates:
                daily_equity = self.current_capital
                
                # 각 심볼에 대해 전략 실행
                for symbol in daily_data.keys():
                    self._process_symbol_day(symbol, date, daily_data[symbol], min5_data[symbol])
                
                # 포지션 가치 계산
                for symbol, position in self.positions.items():
                    if symbol in daily_data:
                        day_data = daily_data[symbol][daily_data[symbol]['date'].dt.date == date]
                        if not day_data.empty:
                            current_price = day_data.iloc[-1]['close']
                            position_value = position['quantity'] * current_price
                            daily_equity += position_value - (position['quantity'] * position['avg_price'])
                
                # 자본 곡선 기록
                self.equity_curve.append({
                    'date': pd.to_datetime(date),
                    'equity': daily_equity,
                    'return': (daily_equity - self.initial_capital) / self.initial_capital
                })
                
        except Exception as e:
            logger.error(f"백테스트 실행 오류: {e}")
    
    def _process_symbol_day(self, symbol: str, date, daily_df: pd.DataFrame, 
                           min5_df: pd.DataFrame):
        """심볼별 일일 처리"""
        try:
            # 해당 날짜 데이터 필터링
            day_data = daily_df[daily_df['date'].dt.date == date]
            if day_data.empty:
                return
            
            day_info = day_data.iloc[0]
            daily_open = day_info['open']
            daily_high = day_info['high']
            
            # 5분봉 데이터 필터링
            day_5min = min5_df[min5_df['timestamp'].dt.date == date]
            if len(day_5min) < 2:
                return
            
            # 전략 조건 확인
            second_5min = day_5min.iloc[1]  # 두 번째 5분봉
            
            # 조건 1: 두 번째 5분봉이 일 시가보다 높음
            condition1 = second_5min['close'] > daily_open
            
            # 조건 2: 현재가가 당일 최고가 돌파 (시뮬레이션)
            current_price = day_5min.iloc[-1]['close']
            condition2 = current_price > daily_high * 1.001  # 0.1% 돌파
            
            # 조건 3: 거래량 조건 (시뮬레이션)
            avg_volume_5min = day_5min['amount'].mean()
            condition3 = avg_volume_5min >= 300000  # 30만 달러
            
            # 매수 조건
            if condition1 and condition2 and condition3 and symbol not in self.positions:
                self._execute_buy(symbol, current_price, date)
            
            # 매도 조건 (기존 포지션이 있는 경우)
            elif symbol in self.positions:
                position = self.positions[symbol]
                pnl_percent = (current_price - position['avg_price']) / position['avg_price']
                
                # 손절: -5%
                if pnl_percent <= -0.05:
                    self._execute_sell(symbol, current_price, date, "손절")
                
                # 익절: 10%, 30%, 100%
                elif pnl_percent >= 1.0:  # 100% 익절
                    self._execute_sell(symbol, current_price, date, "익절 100%")
                elif pnl_percent >= 0.3:  # 30% 익절 (절반)
                    self._execute_partial_sell(symbol, current_price, date, "익절 30%", 0.5)
                elif pnl_percent >= 0.1:  # 10% 익절 (절반)
                    self._execute_partial_sell(symbol, current_price, date, "익절 10%", 0.5)
                    
        except Exception as e:
            logger.error(f"심볼 일일 처리 오류: {e}")
    
    def _execute_buy(self, symbol: str, price: float, date):
        """매수 실행"""
        try:
            max_position_value = config.TRADING_CONFIG['max_position_size']
            quantity = int(max_position_value / price)
            
            if quantity > 0 and self.current_capital >= quantity * price:
                self.positions[symbol] = {
                    'quantity': quantity,
                    'avg_price': price,
                    'entry_date': date
                }
                
                self.current_capital -= quantity * price
                
                self.trade_log.append({
                    'date': pd.to_datetime(date),
                    'symbol': symbol,
                    'action': 'BUY',
                    'quantity': quantity,
                    'price': price,
                    'amount': quantity * price,
                    'reason': '진입 조건 만족'
                })
                
                logger.info(f"매수: {symbol} {quantity}주 @ ${price:.2f}")
                
        except Exception as e:
            logger.error(f"매수 실행 오류: {e}")
    
    def _execute_sell(self, symbol: str, price: float, date, reason: str):
        """매도 실행"""
        try:
            if symbol in self.positions:
                position = self.positions[symbol]
                quantity = position['quantity']
                
                self.current_capital += quantity * price
                
                pnl = (price - position['avg_price']) * quantity
                
                self.trade_log.append({
                    'date': pd.to_datetime(date),
                    'symbol': symbol,
                    'action': 'SELL',
                    'quantity': quantity,
                    'price': price,
                    'amount': quantity * price,
                    'pnl': pnl,
                    'reason': reason
                })
                
                del self.positions[symbol]
                
                logger.info(f"매도: {symbol} {quantity}주 @ ${price:.2f}, PnL: ${pnl:.2f}")
                
        except Exception as e:
            logger.error(f"매도 실행 오류: {e}")
    
    def _execute_partial_sell(self, symbol: str, price: float, date, reason: str, ratio: float):
        """부분 매도 실행"""
        try:
            if symbol in self.positions:
                position = self.positions[symbol]
                sell_quantity = int(position['quantity'] * ratio)
                
                if sell_quantity > 0:
                    self.current_capital += sell_quantity * price
                    
                    pnl = (price - position['avg_price']) * sell_quantity
                    
                    self.trade_log.append({
                        'date': pd.to_datetime(date),
                        'symbol': symbol,
                        'action': 'SELL',
                        'quantity': sell_quantity,
                        'price': price,
                        'amount': sell_quantity * price,
                        'pnl': pnl,
                        'reason': reason
                    })
                    
                    # 포지션 수량 업데이트
                    position['quantity'] -= sell_quantity
                    
                    if position['quantity'] <= 0:
                        del self.positions[symbol]
                    
                    logger.info(f"부분매도: {symbol} {sell_quantity}주 @ ${price:.2f}, PnL: ${pnl:.2f}")
                    
        except Exception as e:
            logger.error(f"부분 매도 실행 오류: {e}")
    
    def _calculate_results(self) -> BacktestResult:
        """결과 계산"""
        try:
            if not self.equity_curve or not self.trade_log:
                return BacktestResult(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, pd.DataFrame(), pd.DataFrame())
            
            equity_df = pd.DataFrame(self.equity_curve)
            trade_df = pd.DataFrame(self.trade_log)
            
            # 기본 수익률 계산
            final_equity = equity_df['equity'].iloc[-1]
            total_return = (final_equity - self.initial_capital) / self.initial_capital
            
            # 연간 수익률
            days = (equity_df['date'].iloc[-1] - equity_df['date'].iloc[0]).days
            annual_return = (1 + total_return) ** (365 / max(days, 1)) - 1
            
            # 샤프 비율
            returns = equity_df['return'].pct_change().dropna()
            sharpe_ratio = returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0
            
            # 최대 낙폭
            running_max = equity_df['equity'].expanding().max()
            drawdown = (equity_df['equity'] - running_max) / running_max
            max_drawdown = drawdown.min()
            
            # 거래 통계
            sell_trades = trade_df[trade_df['action'] == 'SELL']
            total_trades = len(sell_trades)
            winning_trades = len(sell_trades[sell_trades['pnl'] > 0])
            losing_trades = len(sell_trades[sell_trades['pnl'] < 0])
            
            win_rate = winning_trades / total_trades if total_trades > 0 else 0
            
            avg_win = sell_trades[sell_trades['pnl'] > 0]['pnl'].mean() if winning_trades > 0 else 0
            avg_loss = sell_trades[sell_trades['pnl'] < 0]['pnl'].mean() if losing_trades > 0 else 0
            
            profit_factor = abs(avg_win * winning_trades / (avg_loss * losing_trades)) if avg_loss != 0 and losing_trades > 0 else 0
            
            return BacktestResult(
                total_return=total_return,
                annual_return=annual_return,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                win_rate=win_rate,
                profit_factor=profit_factor,
                total_trades=total_trades,
                winning_trades=winning_trades,
                losing_trades=losing_trades,
                avg_win=avg_win,
                avg_loss=avg_loss,
                equity_curve=equity_df,
                trade_log=trade_df
            )
            
        except Exception as e:
            logger.error(f"결과 계산 오류: {e}")
            return BacktestResult(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, pd.DataFrame(), pd.DataFrame())
