"""
한국투자증권 Open API 자동매매 프로그램 설정
"""
import os
from dotenv import load_dotenv

load_dotenv()

# 한국투자증권 API 설정
KIS_APP_KEY = os.getenv('KIS_APP_KEY')
KIS_APP_SECRET = os.getenv('KIS_APP_SECRET')
KIS_BASE_URL = 'https://openapi.koreainvestment.com:9443'
KIS_WEBSOCKET_URL = 'ws://ops.koreainvestment.com:21000'

# 거래 설정
TRADING_CONFIG = {
    'account_number': os.getenv('ACCOUNT_NUMBER'),
    'account_product_code': os.getenv('ACCOUNT_PRODUCT_CODE', '01'),
    'max_position_size': 10000,  # 최대 포지션 크기 (USD)
    'min_trade_amount': 300000,  # 최소 거래량 (USD)
    'stop_loss_percent': 0.05,   # 5% 손절
    'take_profit_levels': [0.1, 0.3, 1.0],  # 10%, 30%, 100% 익절
    'take_profit_ratios': [0.5, 0.5, 1.0],  # 각 단계별 매도 비율
}

# 전략 설정
STRATEGY_CONFIG = {
    'kline_interval': '5m',      # 5분 K선
    'volume_threshold': 300000,  # 거래량 임계값 (USD)
    'breakout_confirmation': True,
    'premarket_high_check': True,
}

# 로깅 설정
LOG_CONFIG = {
    'level': 'INFO',
    'file_path': 'logs/trading.log',
    'max_file_size': '10 MB',
    'backup_count': 5,
}

# 백테스팅 설정
BACKTEST_CONFIG = {
    'start_date': '2024-01-01',
    'end_date': '2024-12-31',
    'initial_capital': 10000,
    'commission_rate': 0.001,
}
