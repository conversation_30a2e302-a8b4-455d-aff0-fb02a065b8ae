"""
미국 주식 스크리너 웹 애플리케이션
"""
from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
import json
from datetime import datetime
from loguru import logger
import sys
import os

# 프로젝트 루트를 Python 경로에 추가
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.screener.us_stock_screener import USStockScreener
from src.visualization.chart_generator import ChartGenerator

app = Flask(__name__)
CORS(app)

# 전역 객체들
screener = None
chart_generator = None

def initialize_components():
    """컴포넌트 초기화"""
    global screener, chart_generator
    try:
        screener = USStockScreener()
        chart_generator = ChartGenerator()
        logger.info("스크리너 애플리케이션 초기화 완료")
        return True
    except Exception as e:
        logger.error(f"컴포넌트 초기화 오류: {e}")
        return False

@app.route('/')
def index():
    """메인 페이지"""
    return render_template('screener.html')

@app.route('/api/screen', methods=['POST'])
def screen_stocks():
    """주식 스크리닝"""
    try:
        data = request.get_json() or {}
        min_gain = data.get('min_gain_percent', 20.0)
        min_volume = data.get('min_volume', 100000)
        
        logger.info(f"스크리닝 요청: 최소 상승률 {min_gain}%, 최소 거래량 {min_volume:,}")
        
        if not screener:
            return jsonify({'success': False, 'error': '스크리너가 초기화되지 않았습니다'})
        
        results = screener.screen_stocks(min_gain, min_volume)
        
        # 결과를 JSON 직렬화 가능한 형태로 변환
        stocks_data = []
        for result in results:
            stocks_data.append({
                'symbol': result.symbol,
                'company_name': result.company_name,
                'current_price': result.current_price,
                'open_price': result.open_price,
                'change_percent': result.change_percent,
                'volume': result.volume,
                'market_cap': result.market_cap,
                'sector': result.sector,
                'timestamp': result.timestamp.isoformat() if result.timestamp else None
            })
        
        return jsonify({
            'success': True,
            'data': stocks_data,
            'count': len(stocks_data),
            'criteria': {
                'min_gain_percent': min_gain,
                'min_volume': min_volume
            }
        })
        
    except Exception as e:
        logger.error(f"스크리닝 API 오류: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/top-gainers')
def get_top_gainers():
    """상위 상승 종목 조회"""
    try:
        limit = request.args.get('limit', 10, type=int)
        
        if not screener:
            return jsonify({'success': False, 'error': '스크리너가 초기화되지 않았습니다'})
        
        results = screener.get_top_gainers(limit)
        
        stocks_data = []
        for result in results:
            stocks_data.append({
                'symbol': result.symbol,
                'company_name': result.company_name,
                'current_price': result.current_price,
                'open_price': result.open_price,
                'change_percent': result.change_percent,
                'volume': result.volume
            })
        
        return jsonify({
            'success': True,
            'data': stocks_data
        })
        
    except Exception as e:
        logger.error(f"상위 상승 종목 조회 오류: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/stock/<symbol>')
def get_stock_detail(symbol):
    """종목 상세 정보"""
    try:
        if not screener:
            return jsonify({'success': False, 'error': '스크리너가 초기화되지 않았습니다'})
        
        detail_info = screener.get_detailed_info(symbol.upper())
        
        if detail_info:
            return jsonify({
                'success': True,
                'data': detail_info
            })
        else:
            return jsonify({'success': False, 'error': '종목 정보를 찾을 수 없습니다'})
            
    except Exception as e:
        logger.error(f"종목 상세 정보 조회 오류: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/chart/<symbol>')
def get_stock_chart(symbol):
    """종목 차트"""
    try:
        chart_type = request.args.get('type', 'daily')
        
        if not chart_generator:
            return jsonify({'success': False, 'error': '차트 생성기가 초기화되지 않았습니다'})
        
        if chart_type == 'intraday':
            chart_html = chart_generator.generate_intraday_chart(symbol.upper())
        elif chart_type == 'mini':
            chart_html = chart_generator.generate_mini_chart(symbol.upper())
        else:
            chart_html = chart_generator.generate_candlestick_chart(symbol.upper())
        
        if chart_html:
            return jsonify({
                'success': True,
                'chart_html': chart_html
            })
        else:
            return jsonify({'success': False, 'error': '차트를 생성할 수 없습니다'})
            
    except Exception as e:
        logger.error(f"차트 생성 오류: {e}")
        return jsonify({'success': False, 'error': str(e)})

@app.route('/api/refresh')
def refresh_data():
    """데이터 새로고침"""
    try:
        if screener:
            screener.refresh_data()
        
        return jsonify({
            'success': True,
            'message': '데이터가 새로고침되었습니다',
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        logger.error(f"데이터 새로고침 오류: {e}")
        return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    # 로깅 설정
    logger.remove()
    logger.add(
        sink=lambda msg: print(msg, end=""),
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    print("🚀 미국 주식 스크리너 웹 애플리케이션 시작")
    print("=" * 50)
    
    # 컴포넌트 초기화
    if initialize_components():
        print("\n📊 스크리너 정보:")
        print("- URL: http://localhost:5001")
        print("- 기능: 개장 후 20% 이상 상승 종목 검색")
        print("- 차트: 실시간 K선 차트 제공")
        print("- 데이터: 모의 데이터 사용 중")
        
        print("\n🔍 주요 기능:")
        print("- 조건별 종목 스크리닝")
        print("- 상위 상승 종목 조회")
        print("- 종목별 상세 정보")
        print("- 일봉/일중 K선 차트")
        print("- 실시간 데이터 새로고침")
        
        print("\n" + "=" * 50)
        print("웹 서버를 시작합니다...")
        
        try:
            app.run(host='0.0.0.0', port=5001, debug=False)
        except KeyboardInterrupt:
            print("\n\n👋 서버가 중지되었습니다.")
        except Exception as e:
            print(f"\n❌ 서버 실행 중 오류: {e}")
    else:
        print("❌ 컴포넌트 초기화 실패")
        sys.exit(1)
