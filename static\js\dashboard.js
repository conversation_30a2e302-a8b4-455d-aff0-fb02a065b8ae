// 대시보드 JavaScript
class TradingDashboard {
    constructor() {
        this.socket = io();
        this.isConnected = false;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupWebSocket();
        this.loadInitialData();
        this.startPeriodicUpdates();
    }

    setupEventListeners() {
        // 거래 제어 버튼
        document.getElementById('startTradingBtn').addEventListener('click', () => this.startTrading());
        document.getElementById('stopTradingBtn').addEventListener('click', () => this.stopTrading());
        
        // 백테스트 폼
        document.getElementById('backtestForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.runBacktest();
        });
    }

    setupWebSocket() {
        this.socket.on('connect', () => {
            console.log('웹소켓 연결됨');
            this.isConnected = true;
            this.updateConnectionStatus(true);
        });

        this.socket.on('disconnect', () => {
            console.log('웹소켓 연결 해제됨');
            this.isConnected = false;
            this.updateConnectionStatus(false);
        });

        this.socket.on('portfolio_update', (data) => {
            this.updatePortfolioStats(data);
        });

        this.socket.on('order_update', (data) => {
            this.showNotification('새로운 주문이 체결되었습니다', 'success');
            this.refreshOrders();
        });
    }

    updateConnectionStatus(connected) {
        const statusIndicator = document.getElementById('systemStatus');
        const statusText = document.getElementById('systemStatusText');
        
        if (connected) {
            statusIndicator.className = 'status-indicator status-active';
            statusText.textContent = '시스템 연결됨';
        } else {
            statusIndicator.className = 'status-indicator status-inactive';
            statusText.textContent = '시스템 연결 끊어짐';
        }
    }

    async loadInitialData() {
        try {
            await Promise.all([
                this.loadSystemStatus(),
                this.loadPortfolio(),
                this.loadOrders(),
                this.loadMarketData()
            ]);
        } catch (error) {
            console.error('초기 데이터 로딩 오류:', error);
            this.showNotification('데이터 로딩 중 오류가 발생했습니다', 'error');
        }
    }

    async loadSystemStatus() {
        try {
            const response = await fetch('/api/status');
            const result = await response.json();
            
            if (result.success) {
                const data = result.data;
                this.updateSystemStatus(data);
            }
        } catch (error) {
            console.error('시스템 상태 로딩 오류:', error);
        }
    }

    async loadPortfolio() {
        try {
            const response = await fetch('/api/portfolio');
            const result = await response.json();
            
            if (result.success) {
                this.updatePortfolio(result.data);
            }
        } catch (error) {
            console.error('포트폴리오 로딩 오류:', error);
        }
    }

    async loadOrders() {
        try {
            const response = await fetch('/api/orders');
            const result = await response.json();
            
            if (result.success) {
                this.updateOrdersTable(result.data);
            }
        } catch (error) {
            console.error('주문 내역 로딩 오류:', error);
        }
    }

    async loadMarketData() {
        const symbols = ['AAPL', 'TSLA', 'NVDA', 'MSFT'];
        const marketDataDiv = document.getElementById('marketData');
        
        try {
            marketDataDiv.innerHTML = '';
            
            for (const symbol of symbols) {
                const response = await fetch(`/api/market/${symbol}`);
                const result = await response.json();
                
                if (result.success) {
                    const data = result.data;
                    const changeClass = data.change >= 0 ? 'profit' : 'loss';
                    const changeIcon = data.change >= 0 ? 'fa-arrow-up' : 'fa-arrow-down';
                    
                    const marketItem = document.createElement('div');
                    marketItem.className = 'border-bottom pb-2 mb-2';
                    marketItem.innerHTML = `
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <strong>${symbol}</strong>
                                <div class="small text-muted">현재가</div>
                            </div>
                            <div class="text-end">
                                <div class="h6 mb-0">$${data.price.toFixed(2)}</div>
                                <div class="small ${changeClass}">
                                    <i class="fas ${changeIcon}"></i>
                                    ${data.change >= 0 ? '+' : ''}${data.change.toFixed(2)} (${data.change_rate.toFixed(2)}%)
                                </div>
                            </div>
                        </div>
                    `;
                    marketDataDiv.appendChild(marketItem);
                }
            }
        } catch (error) {
            console.error('시장 데이터 로딩 오류:', error);
            marketDataDiv.innerHTML = '<div class="text-center text-danger">데이터 로딩 실패</div>';
        }
    }

    updateSystemStatus(data) {
        const statusIndicator = document.getElementById('systemStatus');
        const statusText = document.getElementById('systemStatusText');
        
        if (data.system_status === 'running') {
            statusIndicator.className = 'status-indicator status-active';
            statusText.textContent = data.is_trading_active ? '자동매매 활성' : '시스템 대기';
        } else {
            statusIndicator.className = 'status-indicator status-inactive';
            statusText.textContent = '시스템 중지';
        }
        
        // 버튼 상태 업데이트
        document.getElementById('startTradingBtn').disabled = data.is_trading_active;
        document.getElementById('stopTradingBtn').disabled = !data.is_trading_active;
    }

    updatePortfolio(data) {
        document.getElementById('totalValue').textContent = `$${data.total_value.toFixed(2)}`;
        
        const totalPnLElement = document.getElementById('totalPnL');
        totalPnLElement.textContent = `$${data.total_pnl.toFixed(2)}`;
        totalPnLElement.className = data.total_pnl >= 0 ? 'profit' : 'loss';
        
        document.getElementById('dailyTrades').textContent = data.daily_trade_count;
        document.getElementById('totalPositions').textContent = data.total_positions;
        
        this.updatePositionsTable(data.positions);
    }

    updatePortfolioStats(data) {
        // 실시간 업데이트용
        document.getElementById('totalValue').textContent = `$${data.total_value.toFixed(2)}`;
        
        const totalPnLElement = document.getElementById('totalPnL');
        totalPnLElement.textContent = `$${data.total_pnl.toFixed(2)}`;
        totalPnLElement.className = data.total_pnl >= 0 ? 'profit' : 'loss';
    }

    updatePositionsTable(positions) {
        const tbody = document.getElementById('positionsTable');
        
        if (!positions || Object.keys(positions).length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">포지션이 없습니다</td></tr>';
            return;
        }
        
        tbody.innerHTML = '';
        
        Object.values(positions).forEach(position => {
            const pnl = position.unrealized_pnl;
            const pnlPercent = position.unrealized_pnl_percent * 100;
            const pnlClass = pnl >= 0 ? 'profit' : 'loss';
            
            const row = document.createElement('tr');
            row.innerHTML = `
                <td><strong>${position.symbol}</strong></td>
                <td>${position.quantity}</td>
                <td>$${position.avg_price.toFixed(2)}</td>
                <td>$${position.current_price.toFixed(2)}</td>
                <td class="${pnlClass}">$${pnl.toFixed(2)}</td>
                <td class="${pnlClass}">${pnlPercent >= 0 ? '+' : ''}${pnlPercent.toFixed(2)}%</td>
            `;
            tbody.appendChild(row);
        });
    }

    updateOrdersTable(orders) {
        const tbody = document.getElementById('ordersTable');
        
        if (!orders || orders.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">거래 내역이 없습니다</td></tr>';
            return;
        }
        
        tbody.innerHTML = '';
        
        orders.slice(-10).reverse().forEach(order => { // 최근 10개만 표시
            const sideClass = order.side === 'BUY' ? 'text-success' : 'text-danger';
            const sideIcon = order.side === 'BUY' ? 'fa-arrow-up' : 'fa-arrow-down';
            
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${new Date(order.created_at).toLocaleString()}</td>
                <td><strong>${order.symbol}</strong></td>
                <td class="${sideClass}">
                    <i class="fas ${sideIcon}"></i> ${order.side}
                </td>
                <td>${order.quantity}</td>
                <td>$${order.price.toFixed(2)}</td>
                <td><span class="badge bg-success">${order.status}</span></td>
                <td class="small">${order.reason}</td>
            `;
            tbody.appendChild(row);
        });
    }

    async startTrading() {
        try {
            const response = await fetch('/api/trading/start', { method: 'POST' });
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('자동매매가 시작되었습니다', 'success');
                document.getElementById('startTradingBtn').disabled = true;
                document.getElementById('stopTradingBtn').disabled = false;
            } else {
                this.showNotification(result.error, 'error');
            }
        } catch (error) {
            console.error('거래 시작 오류:', error);
            this.showNotification('거래 시작 중 오류가 발생했습니다', 'error');
        }
    }

    async stopTrading() {
        try {
            const response = await fetch('/api/trading/stop', { method: 'POST' });
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('자동매매가 중지되었습니다', 'warning');
                document.getElementById('startTradingBtn').disabled = false;
                document.getElementById('stopTradingBtn').disabled = true;
            } else {
                this.showNotification(result.error, 'error');
            }
        } catch (error) {
            console.error('거래 중지 오류:', error);
            this.showNotification('거래 중지 중 오류가 발생했습니다', 'error');
        }
    }

    async runBacktest() {
        const symbols = document.getElementById('backtestSymbols').value.split(',').map(s => s.trim());
        const startDate = document.getElementById('backtestStartDate').value;
        const endDate = document.getElementById('backtestEndDate').value;
        const initialCapital = parseFloat(document.getElementById('backtestCapital').value);
        
        try {
            this.showNotification('백테스트를 실행 중입니다...', 'info');
            
            const response = await fetch('/api/backtest', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    symbols,
                    start_date: startDate,
                    end_date: endDate,
                    initial_capital: initialCapital
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.displayBacktestResults(result.data);
                this.showNotification('백테스트가 완료되었습니다', 'success');
            } else {
                this.showNotification(result.error, 'error');
            }
        } catch (error) {
            console.error('백테스트 오류:', error);
            this.showNotification('백테스트 중 오류가 발생했습니다', 'error');
        }
    }

    displayBacktestResults(data) {
        const resultsDiv = document.getElementById('backtestResults');
        const contentDiv = document.getElementById('backtestResultsContent');
        
        contentDiv.innerHTML = `
            <div class="row">
                <div class="col-6">
                    <small class="text-muted">총 수익률</small>
                    <div class="fw-bold ${data.total_return >= 0 ? 'text-success' : 'text-danger'}">
                        ${(data.total_return * 100).toFixed(2)}%
                    </div>
                </div>
                <div class="col-6">
                    <small class="text-muted">승률</small>
                    <div class="fw-bold">${(data.win_rate * 100).toFixed(1)}%</div>
                </div>
                <div class="col-6 mt-2">
                    <small class="text-muted">총 거래</small>
                    <div class="fw-bold">${data.total_trades}</div>
                </div>
                <div class="col-6 mt-2">
                    <small class="text-muted">최대 낙폭</small>
                    <div class="fw-bold text-danger">${(data.max_drawdown * 100).toFixed(2)}%</div>
                </div>
            </div>
        `;
        
        resultsDiv.style.display = 'block';
    }

    refreshPositions() {
        this.loadPortfolio();
    }

    refreshOrders() {
        this.loadOrders();
    }

    startPeriodicUpdates() {
        // 현재 시간 업데이트
        setInterval(() => {
            document.getElementById('currentTime').textContent = new Date().toLocaleString();
        }, 1000);
        
        // 데이터 주기적 업데이트
        setInterval(() => {
            this.loadPortfolio();
            this.loadMarketData();
        }, 30000); // 30초마다
    }

    showNotification(message, type = 'info') {
        // 간단한 알림 표시
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        }[type] || 'alert-info';
        
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 80px; right: 20px; z-index: 1050; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alertDiv);
        
        // 5초 후 자동 제거
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }
}

// 페이지 로드 시 대시보드 초기화
document.addEventListener('DOMContentLoaded', () => {
    new TradingDashboard();
});
