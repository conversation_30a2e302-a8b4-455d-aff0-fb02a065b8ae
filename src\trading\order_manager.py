"""
주문 관리 시스템
"""
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
from loguru import logger
import config
from src.api.kis_client import KISClient
from src.strategy.trading_strategy import TradingStrategy, TradingSignal, SignalType


class OrderStatus(Enum):
    """주문 상태"""
    PENDING = "PENDING"
    FILLED = "FILLED"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"
    PARTIAL_FILLED = "PARTIAL_FILLED"


class OrderType(Enum):
    """주문 타입"""
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP_LOSS = "STOP_LOSS"


@dataclass
class Order:
    """주문 정보"""
    order_id: str
    symbol: str
    side: str  # BUY, SELL
    quantity: int
    price: float
    order_type: OrderType
    status: OrderStatus
    filled_quantity: int = 0
    avg_fill_price: float = 0.0
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    reason: str = ""


class RiskManager:
    """리스크 관리"""
    
    def __init__(self):
        self.max_position_size = config.TRADING_CONFIG['max_position_size']
        self.max_daily_loss = self.max_position_size * 0.1  # 일일 최대 손실 10%
        self.daily_pnl = 0.0
        self.daily_trade_count = 0
        self.max_daily_trades = 10
        
    def check_position_size(self, symbol: str, quantity: int, price: float) -> bool:
        """포지션 크기 확인"""
        position_value = quantity * price
        if position_value > self.max_position_size:
            logger.warning(f"포지션 크기 초과: ${position_value:.2f} > ${self.max_position_size:.2f}")
            return False
        return True
    
    def check_daily_loss_limit(self, additional_loss: float = 0) -> bool:
        """일일 손실 한도 확인"""
        total_loss = self.daily_pnl + additional_loss
        if total_loss < -self.max_daily_loss:
            logger.warning(f"일일 손실 한도 초과: ${total_loss:.2f} < ${-self.max_daily_loss:.2f}")
            return False
        return True
    
    def check_trade_frequency(self) -> bool:
        """거래 빈도 확인"""
        if self.daily_trade_count >= self.max_daily_trades:
            logger.warning(f"일일 거래 횟수 초과: {self.daily_trade_count} >= {self.max_daily_trades}")
            return False
        return True
    
    def update_daily_pnl(self, pnl: float):
        """일일 PnL 업데이트"""
        self.daily_pnl += pnl
        logger.info(f"일일 PnL 업데이트: ${self.daily_pnl:.2f}")
    
    def increment_trade_count(self):
        """거래 횟수 증가"""
        self.daily_trade_count += 1
        logger.info(f"일일 거래 횟수: {self.daily_trade_count}")
    
    def reset_daily_stats(self):
        """일일 통계 리셋"""
        self.daily_pnl = 0.0
        self.daily_trade_count = 0
        logger.info("일일 통계 리셋")


class OrderManager:
    """주문 관리자"""
    
    def __init__(self):
        self.kis_client = KISClient()
        self.strategy = TradingStrategy()
        self.risk_manager = RiskManager()
        self.orders: Dict[str, Order] = {}
        self.order_counter = 0
        
    def generate_order_id(self) -> str:
        """주문 ID 생성"""
        self.order_counter += 1
        return f"ORD_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{self.order_counter:04d}"
    
    def place_order(self, signal: TradingSignal, order_type: OrderType = OrderType.MARKET) -> Optional[Order]:
        """주문 실행"""
        try:
            # 리스크 체크
            if not self._validate_order(signal):
                return None
            
            # 주문 생성
            order_id = self.generate_order_id()
            order = Order(
                order_id=order_id,
                symbol=signal.symbol,
                side=signal.signal_type.value,
                quantity=signal.quantity,
                price=signal.price,
                order_type=order_type,
                status=OrderStatus.PENDING,
                reason=signal.reason
            )
            
            # API 주문 실행
            exchange = "NAS"  # 기본값, 실제로는 심볼에 따라 결정
            
            if signal.signal_type == SignalType.BUY:
                api_result = self.kis_client.place_overseas_order(
                    symbol=signal.symbol,
                    exchange=exchange,
                    order_type="BUY",
                    quantity=signal.quantity,
                    price=signal.price if order_type == OrderType.LIMIT else 0
                )
            elif signal.signal_type == SignalType.SELL:
                api_result = self.kis_client.place_overseas_order(
                    symbol=signal.symbol,
                    exchange=exchange,
                    order_type="SELL",
                    quantity=signal.quantity,
                    price=signal.price if order_type == OrderType.LIMIT else 0
                )
            else:
                logger.warning(f"지원하지 않는 신호 타입: {signal.signal_type}")
                return None
            
            if api_result:
                order.status = OrderStatus.FILLED  # 시장가 주문은 즉시 체결로 가정
                order.filled_quantity = signal.quantity
                order.avg_fill_price = signal.price
                order.updated_at = datetime.now()
                
                # 포지션 업데이트
                self.strategy.update_position(
                    symbol=signal.symbol,
                    quantity=signal.quantity,
                    price=signal.price,
                    action=signal.signal_type.value
                )
                
                # 리스크 관리 업데이트
                self.risk_manager.increment_trade_count()
                
                logger.info(f"주문 체결: {order_id} - {signal.symbol} {signal.signal_type.value} {signal.quantity}주 @ ${signal.price:.2f}")
            else:
                order.status = OrderStatus.REJECTED
                logger.error(f"주문 실패: {order_id}")
            
            self.orders[order_id] = order
            return order
            
        except Exception as e:
            logger.error(f"주문 실행 오류: {e}")
            return None
    
    def _validate_order(self, signal: TradingSignal) -> bool:
        """주문 유효성 검증"""
        try:
            # 거래 빈도 체크
            if not self.risk_manager.check_trade_frequency():
                return False
            
            # 포지션 크기 체크 (매수 시)
            if signal.signal_type == SignalType.BUY:
                if not self.risk_manager.check_position_size(
                    signal.symbol, signal.quantity, signal.price
                ):
                    return False
            
            # 일일 손실 한도 체크
            if not self.risk_manager.check_daily_loss_limit():
                return False
            
            # 수량 유효성 체크
            if signal.quantity <= 0:
                logger.warning(f"잘못된 주문 수량: {signal.quantity}")
                return False
            
            # 가격 유효성 체크
            if signal.price <= 0:
                logger.warning(f"잘못된 주문 가격: {signal.price}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"주문 유효성 검증 오류: {e}")
            return False
    
    def cancel_order(self, order_id: str) -> bool:
        """주문 취소"""
        try:
            if order_id not in self.orders:
                logger.warning(f"존재하지 않는 주문 ID: {order_id}")
                return False
            
            order = self.orders[order_id]
            
            if order.status != OrderStatus.PENDING:
                logger.warning(f"취소할 수 없는 주문 상태: {order.status}")
                return False
            
            # API 주문 취소 (실제 구현 시 필요)
            # result = self.kis_client.cancel_overseas_order(...)
            
            order.status = OrderStatus.CANCELLED
            order.updated_at = datetime.now()
            
            logger.info(f"주문 취소: {order_id}")
            return True
            
        except Exception as e:
            logger.error(f"주문 취소 오류: {e}")
            return False
    
    def get_order_status(self, order_id: str) -> Optional[Order]:
        """주문 상태 조회"""
        return self.orders.get(order_id)
    
    def get_active_orders(self) -> List[Order]:
        """활성 주문 조회"""
        return [order for order in self.orders.values() 
                if order.status in [OrderStatus.PENDING, OrderStatus.PARTIAL_FILLED]]
    
    def get_filled_orders(self, symbol: str = None) -> List[Order]:
        """체결된 주문 조회"""
        filled_orders = [order for order in self.orders.values() 
                        if order.status == OrderStatus.FILLED]
        
        if symbol:
            filled_orders = [order for order in filled_orders if order.symbol == symbol]
        
        return filled_orders
    
    def process_trading_signals(self, symbols: List[str]) -> List[Order]:
        """거래 신호 처리"""
        executed_orders = []
        
        try:
            for symbol in symbols:
                # 현재 포지션 확인
                positions = self.strategy.get_current_positions()
                
                if symbol in positions:
                    # 기존 포지션에 대한 청산 신호 확인
                    exit_signal = self.strategy.analyze_exit_conditions(symbol, positions[symbol])
                    if exit_signal and exit_signal.signal_type == SignalType.SELL:
                        order = self.place_order(exit_signal)
                        if order:
                            executed_orders.append(order)
                else:
                    # 새로운 진입 신호 확인
                    if self.strategy.should_trade_symbol(symbol):
                        entry_signal = self.strategy.analyze_entry_conditions(symbol)
                        if entry_signal and entry_signal.signal_type == SignalType.BUY:
                            order = self.place_order(entry_signal)
                            if order:
                                executed_orders.append(order)
            
            return executed_orders
            
        except Exception as e:
            logger.error(f"거래 신호 처리 오류: {e}")
            return executed_orders
    
    def get_portfolio_summary(self) -> Dict:
        """포트폴리오 요약"""
        try:
            positions = self.strategy.get_current_positions()
            
            total_value = 0.0
            total_pnl = 0.0
            total_pnl_percent = 0.0
            
            for position in positions.values():
                position_value = position.quantity * position.current_price
                total_value += position_value
                total_pnl += position.unrealized_pnl
            
            if total_value > 0:
                total_pnl_percent = total_pnl / (total_value - total_pnl)
            
            return {
                'total_positions': len(positions),
                'total_value': total_value,
                'total_pnl': total_pnl,
                'total_pnl_percent': total_pnl_percent,
                'daily_pnl': self.risk_manager.daily_pnl,
                'daily_trade_count': self.risk_manager.daily_trade_count,
                'positions': positions
            }
            
        except Exception as e:
            logger.error(f"포트폴리오 요약 오류: {e}")
            return {}
