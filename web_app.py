"""
웹 인터페이스 - Flask 애플리케이션
"""
from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
from flask_socketio import SocketIO, emit
import json
import threading
import time
from datetime import datetime
from loguru import logger
import config
from src.trading.order_manager import OrderManager
from src.data.market_data import MarketDataCollector
from src.testing.backtester import Backtester


app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
CORS(app)
socketio = SocketIO(app, cors_allowed_origins="*")

# 전역 객체들
order_manager = None
market_data = None
trading_bot_thread = None
is_trading_active = False


def initialize_components():
    """컴포넌트 초기화"""
    global order_manager, market_data
    try:
        order_manager = OrderManager()
        market_data = MarketDataCollector()
        logger.info("웹 애플리케이션 컴포넌트 초기화 완료")
        return True
    except Exception as e:
        logger.error(f"컴포넌트 초기화 오류: {e}")
        return False


@app.route('/')
def dashboard():
    """메인 대시보드"""
    return render_template('dashboard.html')


@app.route('/api/status')
def get_status():
    """시스템 상태 조회"""
    try:
        market_status = market_data.get_market_status() if market_data else {}
        
        return jsonify({
            'success': True,
            'data': {
                'is_trading_active': is_trading_active,
                'market_open': market_status.get('is_open', False),
                'current_time': datetime.now().isoformat(),
                'system_status': 'running' if order_manager else 'stopped'
            }
        })
    except Exception as e:
        logger.error(f"상태 조회 오류: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/portfolio')
def get_portfolio():
    """포트폴리오 조회"""
    try:
        if not order_manager:
            return jsonify({'success': False, 'error': '시스템이 초기화되지 않았습니다'})
        
        portfolio = order_manager.get_portfolio_summary()
        
        # 포지션 데이터를 JSON 직렬화 가능한 형태로 변환
        positions_data = {}
        if 'positions' in portfolio:
            for symbol, position in portfolio['positions'].items():
                positions_data[symbol] = {
                    'symbol': position.symbol,
                    'quantity': position.quantity,
                    'avg_price': position.avg_price,
                    'current_price': position.current_price,
                    'unrealized_pnl': position.unrealized_pnl,
                    'unrealized_pnl_percent': position.unrealized_pnl_percent,
                    'entry_time': position.entry_time.isoformat()
                }
        
        return jsonify({
            'success': True,
            'data': {
                'total_positions': portfolio.get('total_positions', 0),
                'total_value': portfolio.get('total_value', 0),
                'total_pnl': portfolio.get('total_pnl', 0),
                'total_pnl_percent': portfolio.get('total_pnl_percent', 0),
                'daily_pnl': portfolio.get('daily_pnl', 0),
                'daily_trade_count': portfolio.get('daily_trade_count', 0),
                'positions': positions_data
            }
        })
    except Exception as e:
        logger.error(f"포트폴리오 조회 오류: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/orders')
def get_orders():
    """주문 내역 조회"""
    try:
        if not order_manager:
            return jsonify({'success': False, 'error': '시스템이 초기화되지 않았습니다'})
        
        filled_orders = order_manager.get_filled_orders()
        
        orders_data = []
        for order in filled_orders:
            orders_data.append({
                'order_id': order.order_id,
                'symbol': order.symbol,
                'side': order.side,
                'quantity': order.quantity,
                'price': order.price,
                'status': order.status.value,
                'filled_quantity': order.filled_quantity,
                'avg_fill_price': order.avg_fill_price,
                'created_at': order.created_at.isoformat(),
                'reason': order.reason
            })
        
        return jsonify({
            'success': True,
            'data': orders_data
        })
    except Exception as e:
        logger.error(f"주문 내역 조회 오류: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/market/<symbol>')
def get_market_data_api(symbol):
    """시장 데이터 조회"""
    try:
        if not market_data:
            return jsonify({'success': False, 'error': '시스템이 초기화되지 않았습니다'})
        
        price_data = market_data.get_current_price(symbol.upper())
        
        if price_data:
            return jsonify({
                'success': True,
                'data': {
                    'symbol': price_data['symbol'],
                    'price': price_data['price'],
                    'change': price_data['change'],
                    'change_rate': price_data['change_rate'],
                    'volume': price_data['volume'],
                    'high': price_data['high'],
                    'low': price_data['low'],
                    'open': price_data['open'],
                    'timestamp': price_data['timestamp'].isoformat()
                }
            })
        else:
            return jsonify({'success': False, 'error': '데이터를 찾을 수 없습니다'})
            
    except Exception as e:
        logger.error(f"시장 데이터 조회 오류: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/backtest', methods=['POST'])
def run_backtest_api():
    """백테스트 실행"""
    try:
        data = request.get_json()
        symbols = data.get('symbols', ['AAPL', 'TSLA', 'NVDA'])
        start_date = data.get('start_date', '2024-01-01')
        end_date = data.get('end_date', '2024-12-31')
        initial_capital = data.get('initial_capital', 10000)
        
        backtester = Backtester(initial_capital=initial_capital)
        result = backtester.simulate_strategy(symbols, start_date, end_date)
        
        if result:
            return jsonify({
                'success': True,
                'data': {
                    'total_return': result.total_return,
                    'annual_return': result.annual_return,
                    'sharpe_ratio': result.sharpe_ratio,
                    'max_drawdown': result.max_drawdown,
                    'win_rate': result.win_rate,
                    'profit_factor': result.profit_factor,
                    'total_trades': result.total_trades,
                    'winning_trades': result.winning_trades,
                    'losing_trades': result.losing_trades,
                    'avg_win': result.avg_win,
                    'avg_loss': result.avg_loss
                }
            })
        else:
            return jsonify({'success': False, 'error': '백테스트 실행 실패'})
            
    except Exception as e:
        logger.error(f"백테스트 API 오류: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/trading/start', methods=['POST'])
def start_trading():
    """자동매매 시작"""
    global is_trading_active, trading_bot_thread
    
    try:
        if is_trading_active:
            return jsonify({'success': False, 'error': '이미 거래가 활성화되어 있습니다'})
        
        is_trading_active = True
        
        # 별도 스레드에서 거래 로직 실행
        def trading_loop():
            symbols = ["AAPL", "TSLA", "NVDA", "MSFT", "GOOGL"]
            while is_trading_active:
                try:
                    if market_data.is_market_open():
                        executed_orders = order_manager.process_trading_signals(symbols)
                        if executed_orders:
                            # 웹소켓으로 실시간 업데이트 전송
                            socketio.emit('order_update', {
                                'orders': len(executed_orders),
                                'timestamp': datetime.now().isoformat()
                            })
                    time.sleep(60)  # 1분마다 확인
                except Exception as e:
                    logger.error(f"거래 루프 오류: {e}")
                    time.sleep(5)
        
        trading_bot_thread = threading.Thread(target=trading_loop, daemon=True)
        trading_bot_thread.start()
        
        return jsonify({'success': True, 'message': '자동매매가 시작되었습니다'})
        
    except Exception as e:
        logger.error(f"거래 시작 오류: {e}")
        return jsonify({'success': False, 'error': str(e)})


@app.route('/api/trading/stop', methods=['POST'])
def stop_trading():
    """자동매매 중지"""
    global is_trading_active
    
    try:
        is_trading_active = False
        return jsonify({'success': True, 'message': '자동매매가 중지되었습니다'})
        
    except Exception as e:
        logger.error(f"거래 중지 오류: {e}")
        return jsonify({'success': False, 'error': str(e)})


@socketio.on('connect')
def handle_connect():
    """웹소켓 연결"""
    logger.info('클라이언트가 연결되었습니다')
    emit('status', {'message': '연결되었습니다'})


@socketio.on('disconnect')
def handle_disconnect():
    """웹소켓 연결 해제"""
    logger.info('클라이언트 연결이 해제되었습니다')


def start_real_time_updates():
    """실시간 업데이트 스레드"""
    def update_loop():
        while True:
            try:
                if order_manager:
                    portfolio = order_manager.get_portfolio_summary()
                    socketio.emit('portfolio_update', {
                        'total_value': portfolio.get('total_value', 0),
                        'total_pnl': portfolio.get('total_pnl', 0),
                        'timestamp': datetime.now().isoformat()
                    })
                time.sleep(30)  # 30초마다 업데이트
            except Exception as e:
                logger.error(f"실시간 업데이트 오류: {e}")
                time.sleep(5)
    
    update_thread = threading.Thread(target=update_loop, daemon=True)
    update_thread.start()


if __name__ == '__main__':
    # 컴포넌트 초기화
    if initialize_components():
        # 실시간 업데이트 시작
        start_real_time_updates()
        
        logger.info("웹 서버 시작: http://localhost:5000")
        socketio.run(app, host='0.0.0.0', port=5000, debug=True)
    else:
        logger.error("컴포넌트 초기화 실패. 서버를 시작할 수 없습니다.")
